"""
AI Models Module

This module contains AI model configurations and utilities for the CRM backend.
Provides centralized AI model management for consistent usage across the application.
"""

from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv(dotenv_path='.env', override=True)  # pylint: disable=wrong-import-position

from .azure_openai import AzureOpenAIModel, get_azure_openai_model, get_global_azure_openai_model

__all__ = [
    "AzureOpenAIModel",
    "get_azure_openai_model",
    "get_global_azure_openai_model"
]
