"""
Agent Node

Main agent node that processes user input and generates responses.
This node handles message processing, LLM interaction, and state updates.
"""

import logging
from langchain_core.messages import AIMessage

from .base_node import BaseNode
from ..state import (
    LeadAgentState,
    AgentStatus,
    update_state_status,
    set_error,
    increment_iteration
)
from ..tools import LEAD_AGENT_TOOLS

logger = logging.getLogger(__name__)


class AgentNode(BaseNode):
    """
    Main agent node that processes user input and generates responses.
    
    This node handles:
    - Message processing and context management
    - LLM interaction with tool binding
    - Error recovery and retry logic
    - State updates and iteration tracking
    """
    
    def __init__(self, config=None):
        """Initialize the agent node."""
        super().__init__(config)
        
        # Initialize LLM with tools if available
        if self.llm:
            try:
                self.llm_with_tools = self.llm.bind_tools(LEAD_AGENT_TOOLS)
                self.logger.info("Agent node initialized with tool binding")
            except Exception as e:
                self.logger.warning(f"Failed to bind tools to LLM: {e}")
                self.llm_with_tools = None
        else:
            self.llm_with_tools = None
    
    async def execute(self, state: LeadAgentState) -> LeadAgentState:

        """
        Execute the agent node's main functionality.

        Args:
            state: Current agent state

        Returns:
            Updated agent state
        """
        print(f"🤖 [AGENT-NODE] Starting execution with state: {state}")
        print(f"🤖 [AGENT-NODE] User input: {state.get('user_input', 'No input')}")
        print(f"🤖 [AGENT-NODE] Current iteration: {state.get('iteration_count', 0)}")
        try:
            self._log_execution_start(state)
            
            # Update status
            state = update_state_status(state, AgentStatus.THINKING, "processing_input")
            state = increment_iteration(state)
            
            # Check iteration limit
            if state["iteration_count"] > self.config.max_iterations:
                error_msg = "Maximum iterations exceeded. Please try a simpler request."
                self.logger.warning(f"Max iterations exceeded: {state['iteration_count']}")
                return set_error(state, error_msg)
            
            # Prepare messages
            messages = self._prepare_messages(state)
            
            # Generate response
            print(f"🧠 [AGENT-NODE] Processing with LLM, iteration {state['iteration_count']}")
            self.logger.info(f"Agent processing iteration {state['iteration_count']}")

            if self.llm_with_tools is None:
                # Mock response when LLM is not available
                response = AIMessage(
                    content="I'm sorry, the AI service is currently unavailable. Please check your OpenAI API key configuration."
                )
                self.logger.warning("Using mock response due to unavailable LLM")
            else:
                response = await self.llm_with_tools.ainvoke(messages)
                self.logger.debug("Generated LLM response successfully")
            
            # Update state with response
            updated_state = state.copy()
            updated_state["messages"] = messages + [response]
            updated_state["agent_response"] = response.content if hasattr(response, 'content') else str(response)
            
            # Check if tools need to be called
            if hasattr(response, 'tool_calls') and response.tool_calls:
                print(f"🔧 [AGENT-NODE] Tools to be called: {len(response.tool_calls)}")
                for i, tool_call in enumerate(response.tool_calls):
                    print(f"🔧 [AGENT-NODE] Tool {i+1}: {tool_call.get('name', 'unknown')} with args: {tool_call.get('args', {})}")
                updated_state = update_state_status(updated_state, AgentStatus.EXECUTING_TOOL, "calling_tools")
                self.logger.info(f"Tools to be called: {len(response.tool_calls)}")
            else:
                print(f"💬 [AGENT-NODE] Response generated without tool calls: {response.content[:100]}...")
                updated_state = update_state_status(updated_state, AgentStatus.COMPLETED, "response_generated")
                self.logger.info("Response generated without tool calls")
            
            self._log_execution_end(updated_state, success=True)
            return updated_state
            
        except Exception as e:
            self.logger.error(f"Error in agent_node execution: {str(e)}")
            error_state = set_error(state, f"Agent processing failed: {str(e)}")
            self._log_execution_end(error_state, success=False)
            return error_state


# Create global instance
agent_node = AgentNode()
