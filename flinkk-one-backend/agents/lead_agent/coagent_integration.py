"""
CoAgent Integration for Lead Agent

This module provides integration between CopilotKit's CoAgent state
and the Lead Agent tools.
"""

import threading
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)

# Thread-local storage for CoAgent state
_coagent_state = threading.local()


def set_coagent_state(state: Dict[str, Any]) -> None:
    """
    Set the CoAgent state for the current thread.
    
    Args:
        state: CoAgent state dictionary
    """
    _coagent_state.data = state
    logger.info(f"Set CoAgent state: {state}")


def get_coagent_state() -> Optional[Dict[str, Any]]:
    """
    Get the CoAgent state for the current thread.
    
    Returns:
        CoAgent state dictionary or None if not set
    """
    return getattr(_coagent_state, 'data', None)


def get_lead_id_from_state() -> Optional[str]:
    """
    Get the leadId from the CoAgent state.
    
    Returns:
        Lead ID or None if not available
    """
    state = get_coagent_state()
    if state:
        return state.get('leadId')
    return None


def clear_coagent_state() -> None:
    """Clear the CoAgent state for the current thread."""
    _coagent_state.data = None


class CoAgentStateMiddleware:
    """Middleware to handle CoAgent state in CopilotKit requests."""
    
    @staticmethod
    def extract_state_from_request(request_body: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Extract CoAgent state from CopilotKit request.
        
        Args:
            request_body: CopilotKit request body
            
        Returns:
            CoAgent state or None
        """
        # CopilotKit requests may contain state in various formats
        # This is a simplified extraction - may need adjustment based on actual CopilotKit format
        
        # Check for state in the request
        if 'state' in request_body:
            return request_body['state']
        
        # Check for state in messages or other locations
        messages = request_body.get('messages', [])
        for message in messages:
            if isinstance(message, dict) and 'state' in message:
                return message['state']
        
        # Check for state in agent context
        agent_context = request_body.get('agent_context', {})
        if 'state' in agent_context:
            return agent_context['state']
        
        return None
    
    @staticmethod
    def inject_state_into_tools():
        """
        Inject CoAgent state into tool execution context.
        This is called before tool execution to make state available.
        """
        state = get_coagent_state()
        if state:
            logger.info(f"Injecting CoAgent state into tools: {state}")
            # The tools will access this via get_lead_id_from_state()


# Decorator for tools to automatically access CoAgent state
def with_coagent_state(func):
    """
    Decorator that injects CoAgent state into tool functions.
    
    Args:
        func: Tool function to decorate
        
    Returns:
        Decorated function with CoAgent state access
    """
    async def wrapper(*args, **kwargs):
        # Inject leadId from CoAgent state if not provided
        if 'lead_id' not in kwargs or kwargs['lead_id'] is None:
            lead_id = get_lead_id_from_state()
            if lead_id:
                kwargs['lead_id'] = lead_id
                logger.info(f"Injected leadId from CoAgent state: {lead_id}")
        
        return await func(*args, **kwargs)
    
    return wrapper
