"""
Lead Agent Main Class

Enhanced lead agent implementation with modern CopilotKit patterns,
better error handling, and improved state management.
"""

import logging
from typing import Dict, Any, Optional, List
from datetime import datetime
from copilotkit import LangGraphAgent

from .graph import create_lead_agent_graph, run_lead_agent
from .state import LeadAgentState, AgentContext, create_initial_state
from .config import DEFAULT_CONFIG, LeadAgentConfig
from .tools import LEAD_AGENT_TOOLS

logger = logging.getLogger(__name__)


class LeadAgent:
    """
    Enhanced Lead Management Agent with modern CopilotKit integration.
    
    This agent provides:
    - Natural language lead management operations
    - Enhanced error handling and recovery
    - Conversation memory and context tracking
    - Performance monitoring and metrics
    - Flexible configuration and customization
    """
    
    def __init__(
        self,
        config: Optional[LeadAgentConfig] = None,
        enable_memory: bool = True,
        custom_tools: Optional[List] = None
    ):
        """
        Initialize the Lead Agent.
        
        Args:
            config: Agent configuration (optional)
            enable_memory: Enable conversation memory (default: True)
            custom_tools: Additional custom tools (optional)
        """
        self.config = config or DEFAULT_CONFIG
        self.enable_memory = enable_memory
        self.custom_tools = custom_tools or []
        
        # Create the graph
        self.graph = create_lead_agent_graph(
            config=self.config,
            enable_memory=enable_memory
        )
        
        # Initialize metrics
        self.session_metrics = {
            "total_conversations": 0,
            "successful_operations": 0,
            "failed_operations": 0,
            "average_response_time": 0.0,
            "last_activity": None
        }
        
        logger.info("Lead Agent initialized successfully")
    
    async def process_message(
        self,
        message: str,
        context: Optional[Dict[str, Any]] = None,
        thread_id: str = "default"
    ) -> Dict[str, Any]:
        """
        Process a user message and return the agent's response.
        
        Args:
            message: User's input message
            context: Optional context information
            thread_id: Thread ID for conversation memory
            
        Returns:
            Dictionary containing the agent's response and metadata
        """
        start_time = datetime.utcnow()
        
        try:
            logger.info(f"Processing message: {message[:100]}...")
            
            # Update session metrics
            self.session_metrics["total_conversations"] += 1
            self.session_metrics["last_activity"] = start_time
            
            # Run the agent
            final_state = await run_lead_agent(
                user_input=message,
                context=context,
                config=self.config.dict(),
                thread_id=thread_id
            )
            
            # Calculate response time
            response_time = (datetime.utcnow() - start_time).total_seconds()
            
            # Update metrics
            if final_state.get("status") != "error":
                self.session_metrics["successful_operations"] += 1
            else:
                self.session_metrics["failed_operations"] += 1
            
            # Update average response time
            total_ops = self.session_metrics["successful_operations"] + self.session_metrics["failed_operations"]
            current_avg = self.session_metrics["average_response_time"]
            self.session_metrics["average_response_time"] = (
                (current_avg * (total_ops - 1) + response_time) / total_ops
            )
            
            # Prepare response
            response = {
                "message": final_state.get("agent_response", "I'm sorry, I couldn't process your request."),
                "status": final_state.get("status", "unknown"),
                "success": final_state.get("status") != "error",
                "metadata": {
                    "response_time": response_time,
                    "iteration_count": final_state.get("iteration_count", 0),
                    "tool_results": final_state.get("tool_results", []),
                    "thread_id": thread_id
                }
            }
            
            # Add error information if present
            if final_state.get("error_message"):
                response["error"] = final_state["error_message"]
            
            logger.info(f"Message processed successfully in {response_time:.3f}s")
            return response
            
        except Exception as e:
            response_time = (datetime.utcnow() - start_time).total_seconds()
            self.session_metrics["failed_operations"] += 1
            
            logger.error(f"Error processing message: {str(e)}")
            
            return {
                "message": f"I encountered an error while processing your request: {str(e)}",
                "status": "error",
                "success": False,
                "error": str(e),
                "metadata": {
                    "response_time": response_time,
                    "thread_id": thread_id
                }
            }
    
    async def get_conversation_history(self, thread_id: str = "default") -> List[Dict[str, Any]]:
        """
        Get conversation history for a specific thread.
        
        Args:
            thread_id: Thread ID to get history for
            
        Returns:
            List of conversation messages
        """
        try:
            # This would require implementing conversation history retrieval
            # from the memory checkpointer - placeholder for now
            logger.info(f"Getting conversation history for thread: {thread_id}")
            return []
            
        except Exception as e:
            logger.error(f"Error getting conversation history: {str(e)}")
            return []
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get current session metrics."""
        return {
            **self.session_metrics,
            "config": {
                "model": self.config.model_name,
                "temperature": self.config.temperature,
                "max_iterations": self.config.max_iterations,
                "memory_enabled": self.enable_memory
            },
            "tools_available": len(LEAD_AGENT_TOOLS) + len(self.custom_tools)
        }
    
    def reset_metrics(self):
        """Reset session metrics."""
        self.session_metrics = {
            "total_conversations": 0,
            "successful_operations": 0,
            "failed_operations": 0,
            "average_response_time": 0.0,
            "last_activity": None
        }
        logger.info("Session metrics reset")
    
    def update_config(self, **kwargs):
        """Update agent configuration."""
        for key, value in kwargs.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
                logger.info(f"Updated config: {key} = {value}")
    
    def add_custom_tool(self, tool):
        """Add a custom tool to the agent."""
        self.custom_tools.append(tool)
        logger.info(f"Added custom tool: {tool.name if hasattr(tool, 'name') else str(tool)}")
    
    def get_available_tools(self) -> List[str]:
        """Get list of available tool names."""
        tool_names = [tool.name for tool in LEAD_AGENT_TOOLS]
        tool_names.extend([tool.name for tool in self.custom_tools if hasattr(tool, 'name')])
        return tool_names


def create_lead_agent(
    config: Optional[LeadAgentConfig] = None,
    enable_memory: bool = True,
    custom_tools: Optional[List] = None
) -> LeadAgent:
    """
    Factory function to create a Lead Agent instance.
    
    Args:
        config: Agent configuration (optional)
        enable_memory: Enable conversation memory (default: True)
        custom_tools: Additional custom tools (optional)
        
    Returns:
        Configured Lead Agent instance
    """
    return LeadAgent(
        config=config,
        enable_memory=enable_memory,
        custom_tools=custom_tools
    )


def create_copilotkit_lead_agent(
    name: str = "lead_agent",
    description: str = None,
    config: Optional[LeadAgentConfig] = None
) -> LangGraphAgent:
    """
    Create a CopilotKit-compatible LangGraph agent for lead management with CoAgent state support.

    Args:
        name: Agent name
        description: Agent description
        config: Agent configuration (optional)

    Returns:
        CopilotKit LangGraphAgent instance
    """
    if description is None:
        description = """An AI agent specialized in lead management operations including:
        - Updating lead basic information (name, email, phone, company)
        - Managing business context (title, industry, source, website)
        - Handling custom fields and bulk operations
        - Providing intelligent lead management assistance

        The agent has access to the current lead context from the frontend state."""

    # Create the graph with CoAgent state support
    graph = create_lead_agent_graph(config=config)

    return LangGraphAgent(
        name=name,
        description=description,
        graph=graph
    )
