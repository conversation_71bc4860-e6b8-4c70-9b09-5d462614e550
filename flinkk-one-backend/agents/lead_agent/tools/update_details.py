"""
Unified Update Details Tool

Tool for updating any lead information including basic info, business info, and custom fields.
"""

import logging
import async<PERSON>
from typing import Optional, Dict, Any
from datetime import datetime
from langchain_core.tools import tool

from ..action_handlers import bulk_update_lead_fields_handler
from ..coagent_integration import get_lead_id_from_state

logger = logging.getLogger(__name__)


class ToolExecutionError(Exception):
    """Custom exception for tool execution errors."""
    pass


@tool
async def update_details(
    lead_id: Optional[str] = None,
    first_name: Optional[str] = None,
    last_name: Optional[str] = None,
    email: Optional[str] = None,
    phone: Optional[str] = None,
    company: Optional[str] = None,
    title: Optional[str] = None,
    industry: Optional[str] = None,
    source: Optional[str] = None,
    website: Optional[str] = None,
    custom_fields: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Update any lead details including basic information, business information, and custom fields.
    
    This unified tool can handle all types of lead updates in a single call.
    
    Args:
        lead_id: The unique identifier of the lead to update
        first_name: First name of the lead
        last_name: Last name of the lead
        email: Email address of the lead
        phone: Phone number of the lead
        company: Company name of the lead
        title: Job title of the lead
        industry: Industry of the lead's company
        source: Source where the lead came from
        website: Website URL of the lead's company
        custom_fields: Dictionary containing custom field values
        
    Returns:
        Dictionary containing the result of the update operation
    """
    try:
        print(f"🛠️ [UPDATE-DETAILS-TOOL] Starting unified update details tool")
        start_time = datetime.now()
        
        # Get lead ID from CoAgent state if not provided
        if not lead_id:
            print(f"🔍 [UPDATE-DETAILS-TOOL] No lead_id provided, checking CoAgent state...")
            lead_id = get_lead_id_from_state()
            if lead_id:
                print(f"✅ [UPDATE-DETAILS-TOOL] Retrieved lead_id from state: {lead_id}")
            else:
                print(f"❌ [UPDATE-DETAILS-TOOL] No lead_id found in state")
                return {
                    "success": False,
                    "error": "Lead ID is required but not provided and not found in state",
                    "message": "Please provide a lead ID or ensure the lead context is set"
                }
        
        print(f"📝 [UPDATE-DETAILS-TOOL] Processing update for lead: {lead_id}")
        
        # Organize updates by category
        basic_info = {}
        business_info = {}
        
        # Collect basic info updates
        if first_name is not None:
            basic_info["firstName"] = first_name
            print(f"📝 [UPDATE-DETAILS-TOOL] Basic info - firstName: {first_name}")
        if last_name is not None:
            basic_info["lastName"] = last_name
            print(f"📝 [UPDATE-DETAILS-TOOL] Basic info - lastName: {last_name}")
        if email is not None:
            basic_info["email"] = email
            print(f"📝 [UPDATE-DETAILS-TOOL] Basic info - email: {email}")
        if phone is not None:
            basic_info["phone"] = phone
            print(f"📝 [UPDATE-DETAILS-TOOL] Basic info - phone: {phone}")
        if company is not None:
            basic_info["company"] = company
            print(f"📝 [UPDATE-DETAILS-TOOL] Basic info - company: {company}")
        
        # Collect business info updates
        if title is not None:
            business_info["title"] = title
            print(f"📝 [UPDATE-DETAILS-TOOL] Business info - title: {title}")
        if industry is not None:
            business_info["industry"] = industry
            print(f"📝 [UPDATE-DETAILS-TOOL] Business info - industry: {industry}")
        if source is not None:
            business_info["source"] = source
            print(f"📝 [UPDATE-DETAILS-TOOL] Business info - source: {source}")
        if website is not None:
            business_info["website"] = website
            print(f"📝 [UPDATE-DETAILS-TOOL] Business info - website: {website}")
        
        # Handle custom fields
        if custom_fields is None:
            custom_fields = {}
        
        print(f"📋 [UPDATE-DETAILS-TOOL] Summary - Basic info: {basic_info}")
        print(f"📋 [UPDATE-DETAILS-TOOL] Summary - Business info: {business_info}")
        print(f"📋 [UPDATE-DETAILS-TOOL] Summary - Custom fields: {custom_fields}")
        
        # Prepare arguments for the bulk update handler
        args = {
            "leadId": lead_id,
            "updates": {
                "basicInfo": basic_info if basic_info else {},
                "businessInfo": business_info if business_info else {},
                "customFields": custom_fields if custom_fields else {}
            }
        }
        
        print(f"🔧 [UPDATE-DETAILS-TOOL] Calling bulk update handler with args: {args}")
        
        # Execute with error handling
        try:
            result = await bulk_update_lead_fields_handler(args)
            execution_time = (datetime.now() - start_time).total_seconds()
            
            print(f"✅ [UPDATE-DETAILS-TOOL] Update completed successfully in {execution_time:.3f}s")
            print(f"📊 [UPDATE-DETAILS-TOOL] Result: {result}")
            
            return result
            
        except Exception as handler_error:
            execution_time = (datetime.now() - start_time).total_seconds()
            error_msg = f"Handler execution failed: {str(handler_error)}"
            print(f"❌ [UPDATE-DETAILS-TOOL] {error_msg} (after {execution_time:.3f}s)")
            
            return {
                "success": False,
                "error": error_msg,
                "message": "Failed to update lead details. Please try again.",
                "execution_time": execution_time
            }
            
    except Exception as e:
        execution_time = (datetime.now() - start_time).total_seconds() if 'start_time' in locals() else 0
        error_msg = f"Tool execution failed: {str(e)}"
        print(f"❌ [UPDATE-DETAILS-TOOL] {error_msg} (after {execution_time:.3f}s)")
        logger.error(f"Update details tool failed: {error_msg}")
        
        return {
            "success": False,
            "error": error_msg,
            "message": "An unexpected error occurred while updating lead details",
            "execution_time": execution_time
        }


# Tool metadata for registration
update_details.name = "update_details"
update_details.description = """
Update any lead details including basic information, business information, and custom fields.

This unified tool can handle all types of lead updates in a single call:
- Basic info: firstName, lastName, email, phone, company
- Business info: title, industry, source, website  
- Custom fields: any organization-specific fields

Examples:
- Update job title: update_details(title="Software Developer")
- Update contact info: update_details(email="<EMAIL>", phone="+1234567890")
- Update multiple fields: update_details(title="Manager", company="New Company", email="<EMAIL>")
"""
