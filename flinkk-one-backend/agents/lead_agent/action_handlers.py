"""
Action Handlers for Lead Management

This module provides the actual business logic handlers that are called by the tools.
These handlers contain the core functionality for lead management operations.
"""

from typing import Dict, Any
from .database import db_manager
from .validation import validator


async def update_lead_basic_info_handler(args: Dict[str, Any], state: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    Handler for updating lead basic information.
    
    Args:
        args: Dictionary containing leadId and basic info fields
        
    Returns:
        Dictionary with success status and result data
    """
    try:
        # Extract arguments
        lead_id = args.get('leadId') or args.get('lead_id')
        first_name = args.get('firstName') or args.get('first_name')
        last_name = args.get('lastName') or args.get('last_name')
        email = args.get('email')
        phone = args.get('phone')
        company = args.get('company')

        # Use default values (no authentication required)
        tenant_id = "default_tenant"
        user_id = "default_user"

        # Check if leadId is missing and try to get from CoAgent state
        if not lead_id and state:
            lead_id = state.get("leadId")

        # If still no leadId, return error
        if not lead_id:
            return {
                "success": False,
                "message": "Lead ID is required. Please specify which lead you want to update.",
                "error": "Missing lead ID"
            }
        
        # Prepare update data
        update_data = {}
        if first_name is not None:
            update_data['firstName'] = first_name
        if last_name is not None:
            update_data['lastName'] = last_name
        if email is not None:
            update_data['email'] = email
        if phone is not None:
            update_data['phone'] = validator.clean_phone_number(phone)
        if company is not None:
            update_data['company'] = company
        
        # Validate basic info
        is_valid, validation_error = validator.validate_basic_info(update_data)
        if not is_valid:
            return {
                "success": False,
                "message": validation_error,
                "error": "Validation failed"
            }
        
        # Update lead in database
        result = await db_manager.update_lead_basic_info(
            lead_id=lead_id,
            tenant_id=tenant_id,
            user_id=user_id,
            update_data=update_data
        )
        
        return {
            "success": True,
            "message": result["message"],
            "data": {
                "leadId": lead_id,
                "updatedFields": list(update_data.keys()),
                "changes": result["changes"]
            }
        }
        
    except ValueError as e:
        return {
            "success": False,
            "message": str(e),
            "error": "Lead not found or access denied"
        }
    except Exception as e:
        return {
            "success": False,
            "message": "Failed to update lead basic information. Please try again.",
            "error": str(e)
        }


async def update_lead_business_info_handler(args: Dict[str, Any], state: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    Handler for updating lead business information.
    
    Args:
        args: Dictionary containing leadId and business info fields
        
    Returns:
        Dictionary with success status and result data
    """
    try:
        # Extract arguments
        lead_id = args.get('leadId') or args.get('lead_id')
        title = args.get('title')
        industry = args.get('industry')
        source = args.get('source')
        website = args.get('website')
        
        # Use default values (no authentication required)
        tenant_id = "default_tenant"
        user_id = "default_user"

        # Check if leadId is missing and try to get from CoAgent state
        if not lead_id and state:
            lead_id = state.get("leadId")

        # If still no leadId, return error
        if not lead_id:
            return {
                "success": False,
                "message": "Lead ID is required. Please specify which lead you want to update.",
                "error": "Missing lead ID"
            }
        
        # Prepare update data
        update_data = {}
        if title is not None:
            update_data['title'] = title
        if industry is not None:
            update_data['industry'] = industry
        if source is not None:
            update_data['source'] = source
        if website is not None:
            update_data['website'] = website
        
        # Validate business info
        is_valid, validation_error = validator.validate_business_info(update_data)
        if not is_valid:
            return {
                "success": False,
                "message": validation_error,
                "error": "Validation failed"
            }
        
        # Update lead in database
        result = await db_manager.update_lead_business_info(
            lead_id=lead_id,
            tenant_id=tenant_id,
            user_id=user_id,
            update_data=update_data
        )
        
        return {
            "success": True,
            "message": result["message"],
            "data": {
                "leadId": lead_id,
                "updatedFields": list(update_data.keys()),
                "changes": result["changes"]
            }
        }
        
    except ValueError as e:
        return {
            "success": False,
            "message": str(e),
            "error": "Lead not found or access denied"
        }
    except Exception as e:
        return {
            "success": False,
            "message": "Failed to update lead business information. Please try again.",
            "error": str(e)
        }


async def update_lead_custom_fields_handler(args: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handler for updating lead custom fields.
    
    Args:
        args: Dictionary containing leadId and custom_fields
        
    Returns:
        Dictionary with success status and result data
    """
    try:
        # Extract arguments
        lead_id = args.get('leadId') or args.get('lead_id')
        custom_fields = args.get('customFields') or args.get('custom_fields')
        
        # Use default values (no authentication required)
        tenant_id = "default_tenant"
        user_id = "default_user"
        
        # Get custom field definitions
        field_definitions = await db_manager.get_custom_field_definitions(tenant_id)
        
        # Validate custom fields
        is_valid, validation_error, validated_data = validator.validate_custom_fields(
            custom_fields, field_definitions
        )
        if not is_valid:
            return {
                "success": False,
                "message": validation_error,
                "error": "Validation failed"
            }
        
        # Update custom fields in database
        result = await db_manager.update_lead_custom_fields(
            lead_id=lead_id,
            tenant_id=tenant_id,
            user_id=user_id,
            validated_data=validated_data
        )
        
        return {
            "success": True,
            "message": result["message"],
            "data": {
                "leadId": lead_id,
                "updatedFields": result["updated_fields"],
                "changes": result["changes"]
            }
        }
        
    except ValueError as e:
        return {
            "success": False,
            "message": str(e),
            "error": "Lead not found or access denied"
        }
    except Exception as e:
        return {
            "success": False,
            "message": "Failed to update lead custom fields. Please try again.",
            "error": str(e)
        }


async def bulk_update_lead_fields_handler(args: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handler for bulk updating lead fields.
    
    Args:
        args: Dictionary containing leadId and updates object
        
    Returns:
        Dictionary with success status and result data
    """
    try:
        # Extract arguments
        lead_id = args.get('leadId') or args.get('lead_id')
        updates = args.get('updates', {})
        
        # Use default values (no authentication required)
        tenant_id = "default_tenant"
        user_id = "default_user"
        
        # Extract different types of updates
        basic_info = updates.get('basicInfo', {})
        business_info = updates.get('businessInfo', {})
        custom_fields = updates.get('customFields', {})
        
        # Perform bulk update
        result = await db_manager.bulk_update_lead_fields(
            lead_id=lead_id,
            tenant_id=tenant_id,
            user_id=user_id,
            basic_info=basic_info if basic_info else None,
            business_info=business_info if business_info else None,
            custom_fields=custom_fields if custom_fields else None
        )
        
        return {
            "success": True,
            "message": result["message"],
            "data": {
                "leadId": lead_id,
                "updatedCategories": result["updated_categories"],
                "totalChanges": result["total_changes"],
                "changes": result["changes"]
            }
        }
        
    except ValueError as e:
        return {
            "success": False,
            "message": str(e),
            "error": "Lead not found or access denied"
        }
    except Exception as e:
        return {
            "success": False,
            "message": "Failed to perform bulk update. Please try again.",
            "error": str(e)
        }


def get_current_time() -> str:
    """
    Get the current date and time.
    
    Returns:
        Current date and time as a formatted string
    """
    from datetime import datetime
    return datetime.now().strftime("%Y-%m-%d %H:%M:%S")
