"""
Validation utilities for Lead Management Actions

This module provides comprehensive validation for lead data including
basic info, business info, custom fields, and bulk operations.
"""

import re
from typing import Dict, Any, List, Tuple, Optional
from datetime import datetime

# Validation patterns
EMAIL_REGEX = re.compile(r'^[^\s@]+@[^\s@]+\.[^\s@]+$')
URL_REGEX = re.compile(r'^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$')
PHONE_REGEX = re.compile(r'^[\d+\-\s()]+$')

class ValidationError(Exception):
    """Custom exception for validation errors."""
    pass

class LeadValidator:
    """Comprehensive validator for lead data."""
    
    @staticmethod
    def validate_basic_info(data: Dict[str, Any]) -> <PERSON><PERSON>[bool, Optional[str]]:
        """
        Validate basic lead information.
        
        Args:
            data: Dictionary containing basic info fields
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        # Check if at least one field is provided
        basic_fields = ['firstName', 'lastName', 'email', 'phone', 'company']
        provided_fields = [field for field in basic_fields if field in data and data[field] is not None]
        
        if not provided_fields:
            return False, "At least one basic information field must be provided"
        
        # Validate email format if provided
        if 'email' in data and data['email']:
            if not EMAIL_REGEX.match(data['email']):
                return False, "Invalid email format. Please provide a valid email address."
        
        # Validate phone format if provided
        if 'phone' in data and data['phone']:
            cleaned_phone = re.sub(r'[^\d+\-\s()]', '', data['phone'])
            if not cleaned_phone:
                return False, "Invalid phone number format."
        
        # Validate string lengths
        string_fields = ['firstName', 'lastName', 'company']
        for field in string_fields:
            if field in data and data[field] and len(str(data[field])) > 255:
                return False, f"{field} must be less than 255 characters"
        
        return True, None
    
    @staticmethod
    def validate_business_info(data: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
        """
        Validate business lead information.
        
        Args:
            data: Dictionary containing business info fields
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        # Check if at least one field is provided
        business_fields = ['title', 'industry', 'source', 'website']
        provided_fields = [field for field in business_fields if field in data and data[field] is not None]
        
        if not provided_fields:
            return False, "At least one business information field must be provided"
        
        # Validate website URL format if provided
        if 'website' in data and data['website']:
            if not URL_REGEX.match(data['website']):
                return False, "Invalid website URL format. Please include http:// or https:// and provide a valid domain."
        
        # Validate string lengths
        string_fields = ['title', 'industry', 'source']
        for field in string_fields:
            if field in data and data[field] and len(str(data[field])) > 255:
                return False, f"{field} must be less than 255 characters"
        
        return True, None
    
    @staticmethod
    def validate_custom_field_value(value: Any, field_definition: Dict[str, Any]) -> Tuple[bool, Optional[str], Any]:
        """
        Validate custom field value based on field type.
        
        Args:
            value: The value to validate
            field_definition: Custom field definition with type and options
            
        Returns:
            Tuple of (is_valid, error_message, validated_value)
        """
        field_type = field_definition.get('type')
        field_label = field_definition.get('label', 'Custom field')
        
        if value is None or value == '':
            return True, None, ''
        
        try:
            if field_type == 'TEXT':
                if not isinstance(value, str):
                    return False, f"{field_label} expects a text value", None
                if len(value) > 1000:  # Reasonable limit for text fields
                    return False, f"{field_label} text is too long (max 1000 characters)", None
                return True, None, value
                
            elif field_type == 'NUMBER':
                try:
                    num_value = float(value)
                    return True, None, str(num_value)
                except (ValueError, TypeError):
                    return False, f"{field_label} expects a numeric value", None
                    
            elif field_type == 'DATE':
                try:
                    if isinstance(value, str):
                        date_value = datetime.fromisoformat(value.replace('Z', '+00:00'))
                    else:
                        date_value = datetime.fromisoformat(str(value))
                    return True, None, date_value.strftime('%Y-%m-%d')
                except (ValueError, TypeError):
                    return False, f"{field_label} expects a valid date (YYYY-MM-DD format)", None
                    
            elif field_type == 'SELECT':
                options = field_definition.get('options', [])
                if options and value not in options:
                    return False, f"{field_label} must be one of: {', '.join(options)}", None
                return True, None, value
                
            elif field_type == 'BOOLEAN':
                if isinstance(value, bool):
                    return True, None, 'true' if value else 'false'
                elif value in ['true', 'false', True, False]:
                    return True, None, 'true' if value in ['true', True] else 'false'
                else:
                    return False, f"{field_label} expects a boolean value (true/false)", None
                    
            else:
                # Default to string for unknown types
                return True, None, str(value)
                
        except Exception as e:
            return False, f"Validation error for {field_label}: {str(e)}", None
    
    @staticmethod
    def validate_custom_fields(
        custom_fields: Dict[str, Any], 
        field_definitions: List[Dict[str, Any]]
    ) -> Tuple[bool, Optional[str], Dict[str, Any]]:
        """
        Validate all custom fields data.
        
        Args:
            custom_fields: Dictionary of field_id -> value
            field_definitions: List of custom field definitions
            
        Returns:
            Tuple of (is_valid, error_message, validated_data)
        """
        if not custom_fields or not isinstance(custom_fields, dict):
            return False, "Custom fields must be provided as an object", {}
        
        # Create field definitions map
        field_def_map = {field['id']: field for field in field_definitions}
        
        validated_data = {}
        
        for field_id, value in custom_fields.items():
            if field_id not in field_def_map:
                return False, f"Custom field with ID '{field_id}' not found or not available for leads", {}
            
            field_def = field_def_map[field_id]
            is_valid, error_msg, validated_value = LeadValidator.validate_custom_field_value(value, field_def)
            
            if not is_valid:
                return False, error_msg, {}
            
            validated_data[field_id] = {
                'value': validated_value,
                'field_definition': field_def
            }
        
        return True, None, validated_data
    
    @staticmethod
    def validate_authentication_context(tenant_id: Optional[str], user_id: Optional[str]) -> Tuple[bool, Optional[str]]:
        """
        Validate authentication context.

        Args:
            tenant_id: Tenant ID from authentication
            user_id: User ID from authentication

        Returns:
            Tuple of (is_valid, error_message)
        """
        # Authentication is not required in this setup
        return True, None
    
    @staticmethod
    def clean_phone_number(phone: str) -> str:
        """Clean phone number by removing invalid characters."""
        if not phone:
            return phone
        return re.sub(r'[^\d+\-\s()]', '', phone)

# Global validator instance
validator = LeadValidator()
