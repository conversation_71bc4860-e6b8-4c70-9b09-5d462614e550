"""
Crew Coordinator

This module orchestrates the multi-agent AI system for generating actions
from call notes using LangChain-based agents.
"""

import logging
import async<PERSON>
from typing import Dict, Any

from .email_draft_agent import EmailDraftAgent
from .task_creation_agent import TaskCreationAgent
from .event_creation_agent import EventCreationAgent

logger = logging.getLogger(__name__)


class CrewCoordinator:
    """Coordinates multiple AI agents to generate comprehensive actions from call notes."""
    
    def __init__(self):
        """Initialize the crew coordinator with all agents."""
        self.email_agent = EmailDraftAgent()
        self.task_agent = TaskCreationAgent()
        self.event_agent = EventCreationAgent()
    
    async def generate_all_actions(self, note_content: str, lead_context: str = "", parallel: bool = True) -> Dict[str, Any]:
        """
        Generate all types of actions (emails, tasks, events) from call notes.

        Args:
            note_content: The call notes content to analyze
            lead_context: Context about the lead/contact
            parallel: Whether to run agents in parallel (default: True)

        Returns:
            Dict: Combined results from all agents
        """
        try:
            logger.info("Starting multi-agent action generation")

            if parallel:
                return await self._generate_actions_parallel(note_content, lead_context)
            else:
                return await self._generate_actions_sequential(note_content, lead_context)

        except Exception as e:
            logger.error(f"Error in crew coordination: {e}")
            return {
                "emails": [],
                "tasks": [],
                "events": [],
                "summary": "",
                "success": False,
                "error": str(e)
            }
    
    async def _generate_actions_parallel(self, note_content: str, lead_context: str) -> Dict[str, Any]:
        """
        Generate actions using parallel execution of agents.

        Args:
            note_content: The call notes content to analyze
            lead_context: Context about the lead/contact

        Returns:
            Dict: Combined results from all agents
        """
        results = {
            "emails": [],
            "tasks": [],
            "events": [],
            "summary": "",
            "success": True,
            "error": None
        }

        try:
            # Execute all agents concurrently
            email_task = self.email_agent.generate_emails(note_content, lead_context)
            task_task = self.task_agent.generate_tasks(note_content, lead_context)
            event_task = self.event_agent.generate_events(note_content, lead_context)

            # Wait for all tasks to complete
            emails, tasks, events = await asyncio.gather(
                email_task, task_task, event_task,
                return_exceptions=True
            )

            # Handle results and exceptions
            results["emails"] = emails if not isinstance(emails, Exception) else []
            results["tasks"] = tasks if not isinstance(tasks, Exception) else []
            results["events"] = events if not isinstance(events, Exception) else []

            # Log any exceptions
            if isinstance(emails, Exception):
                logger.error(f"Error in email agent: {emails}")
            if isinstance(tasks, Exception):
                logger.error(f"Error in task agent: {tasks}")
            if isinstance(events, Exception):
                logger.error(f"Error in event agent: {events}")

            logger.info(f"Parallel generation complete: {len(results['emails'])} emails, {len(results['tasks'])} tasks, {len(results['events'])} events")

        except Exception as e:
            logger.error(f"Error in parallel generation: {e}")
            results["success"] = False
            results["error"] = str(e)

        # Generate summary
        results["summary"] = self._generate_summary(results)

        return results
    
    async def _generate_actions_sequential(self, note_content: str, lead_context: str) -> Dict[str, Any]:
        """
        Generate actions using sequential execution of agents.

        Args:
            note_content: The call notes content to analyze
            lead_context: Context about the lead/contact

        Returns:
            Dict: Combined results from all agents
        """
        results = {
            "emails": [],
            "tasks": [],
            "events": [],
            "summary": "",
            "success": True,
            "error": None
        }

        try:
            # Generate emails
            logger.info("Generating emails...")
            results["emails"] = await self.email_agent.generate_emails(note_content, lead_context)

            # Generate tasks
            logger.info("Generating tasks...")
            results["tasks"] = await self.task_agent.generate_tasks(note_content, lead_context)

            # Generate events
            logger.info("Generating events...")
            results["events"] = await self.event_agent.generate_events(note_content, lead_context)

            # Generate summary
            results["summary"] = self._generate_summary(results)

        except Exception as e:
            logger.error(f"Error in sequential generation: {e}")
            results["success"] = False
            results["error"] = str(e)

        return results
    
    def _generate_summary(self, results: Dict[str, Any]) -> str:
        """
        Generate a summary of all generated actions.
        
        Args:
            results: Results from all agents
            
        Returns:
            str: Summary text
        """
        email_count = len(results.get("emails", []))
        task_count = len(results.get("tasks", []))
        event_count = len(results.get("events", []))
        
        total_actions = email_count + task_count + event_count
        
        if total_actions == 0:
            return "No actionable items were identified from the call notes."
        
        summary_parts = []
        
        if email_count > 0:
            summary_parts.append(f"{email_count} email draft{'s' if email_count != 1 else ''}")
        
        if task_count > 0:
            summary_parts.append(f"{task_count} task{'s' if task_count != 1 else ''}")
        
        if event_count > 0:
            summary_parts.append(f"{event_count} calendar event{'s' if event_count != 1 else ''}")
        
        summary = f"Generated {', '.join(summary_parts)} based on the call notes analysis."
        
        return summary
    
    def format_actions_for_response(self, results: Dict[str, Any], reference_modal: str, reference_modal_id: str) -> Dict[str, Any]:
        """
        Format the generated actions for API response with minimal information.

        Args:
            results: Raw results from agents
            reference_modal: Source model type (e.g., "Lead", "Note")
            reference_modal_id: Source model ID

        Returns:
            Dict: Formatted actions for response with only type, title, and agentCalling
        """
        formatted_actions = []

        # Format emails - only essential fields
        for email in results.get("emails", []):
            formatted_actions.append({
                "type": "email",
                "title": email.get("subject", "Email Draft"),
                "agentCalling": "EmailDraftAgent"
            })

        # Format tasks - only essential fields
        for task in results.get("tasks", []):
            formatted_actions.append({
                "type": "task",
                "title": task.get("title", "Task"),
                "agentCalling": "TaskCreationAgent"
            })

        # Format events - only essential fields
        for event in results.get("events", []):
            formatted_actions.append({
                "type": "event",
                "title": event.get("title", "Event"),
                "agentCalling": "EventCreationAgent"
            })

        return {
            "actions": formatted_actions,
            "summary": results.get("summary", ""),
            "total_count": len(formatted_actions),
            "success": results.get("success", True),
            "error": results.get("error")
        }
