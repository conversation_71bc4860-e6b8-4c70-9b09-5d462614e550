"""
Task Creation Agent

This module provides the specialized agent for creating actionable tasks and to-dos
based on call notes and commitments using LangChain.
"""

import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from langchain.schema import HumanMessage, SystemMessage

from core.prompt_loader import prompt_loader
from ai_models.azure_openai import get_azure_openai_model

logger = logging.getLogger(__name__)


class TaskCreationAgent:
    """Specialized agent for creating actionable tasks from call notes."""

    def __init__(self):
        """Initialize the task creation agent."""
        self.llm = get_azure_openai_model(
            temperature=0.3,
            max_tokens=3000  # Increased token limit to prevent truncation
        )
        self.system_message = SystemMessage(content="""You are an expert task management specialist with deep experience in
        project management, CRM systems, and business process optimization. You excel at
        identifying actionable items, setting appropriate priorities, and creating clear,
        measurable tasks that drive business results. Your task breakdowns are known for
        being comprehensive, realistic, and outcome-focused.

        Your goal is to extract and create specific, actionable tasks from call notes and business commitments.

        CRITICAL: You must respond with valid JSON only. Do not include any explanatory text.""")
    
    async def _generate_task_response(self, note_content: str, lead_context: str = "") -> str:
        """
        Generate task recommendations using LangChain directly.

        Args:
            note_content: The call notes content to analyze
            lead_context: Context about the lead/contact

        Returns:
            str: AI response with task recommendations
        """
        prompt = prompt_loader.get_crew_task_agent_prompt(note_content, lead_context)

        messages = [
            self.system_message,
            HumanMessage(content=prompt)
        ]

        response = await self.llm.create_chat_completion_async(messages)
        return response.content

    async def generate_tasks(self, note_content: str, lead_context: str = "") -> List[Dict[str, Any]]:
        """
        Generate actionable tasks based on call notes.

        Args:
            note_content: The call notes content to analyze
            lead_context: Context about the lead/contact

        Returns:
            List[Dict]: List of generated tasks
        """
        try:
            logger.info("Starting task generation")

            # Generate response using LangChain
            ai_response = await self._generate_task_response(note_content, lead_context)

            # Log the raw AI response for debugging
            logger.debug(f"Raw AI response for tasks: {ai_response[:500]}...")

            # Check if response is valid
            if not ai_response or len(ai_response.strip()) < 10:
                logger.warning("AI returned empty or very short response for tasks")
                return []

            # Check for truncated responses (common patterns)
            truncated_patterns = [
                "and end with",
                "Start with {",
                "End with }",
                "No markdown",
                "No explanatory",
                "Valid JSON syntax"
            ]

            if any(pattern in ai_response for pattern in truncated_patterns):
                logger.warning(f"Detected truncated AI response for tasks: {ai_response}")
                return []

            # Parse the result
            tasks = self._parse_task_result(ai_response)

            # Print task generation results
            print(f"\n✅ TASK AGENT RESULTS: Generated {len(tasks)} tasks")
            for i, task in enumerate(tasks, 1):
                print(f"  {i}. {task.get('title', 'No title')} (Priority: {task.get('priority', 'unknown')})")
            print()

            logger.info(f"Generated {len(tasks)} tasks")
            return tasks

        except Exception as e:
            print(f"\n❌ TASK AGENT ERROR: {type(e).__name__}: {e}")
            logger.error(f"Error generating tasks: {e}")
            return []
    
    def _parse_task_result(self, result: str) -> List[Dict[str, Any]]:
        """
        Parse the agent result to extract tasks.

        Args:
            result: Raw result from the agent

        Returns:
            List[Dict]: Parsed tasks
        """
        try:
            if not result or not isinstance(result, str):
                logger.warning("Empty or invalid result received")
                return []

            # Clean the result string
            result = result.strip()

            # Try multiple JSON extraction strategies
            json_data = None

            # Strategy 1: Look for JSON block markers
            if "```json" in result:
                start_marker = result.find("```json") + 7
                end_marker = result.find("```", start_marker)
                if end_marker != -1:
                    json_str = result[start_marker:end_marker].strip()
                    try:
                        json_data = json.loads(json_str)
                    except json.JSONDecodeError:
                        pass

            # Strategy 2: Look for first complete JSON object
            if json_data is None:
                start_idx = result.find('{')
                if start_idx != -1:
                    # Find the matching closing brace
                    brace_count = 0
                    end_idx = start_idx
                    for i, char in enumerate(result[start_idx:], start_idx):
                        if char == '{':
                            brace_count += 1
                        elif char == '}':
                            brace_count -= 1
                            if brace_count == 0:
                                end_idx = i + 1
                                break

                    if brace_count == 0:
                        json_str = result[start_idx:end_idx]
                        try:
                            json_data = json.loads(json_str)
                        except json.JSONDecodeError:
                            pass

            # Strategy 3: Try parsing the entire result as JSON
            if json_data is None:
                try:
                    json_data = json.loads(result)
                except json.JSONDecodeError:
                    pass

            # Extract tasks from parsed JSON
            if json_data and isinstance(json_data, dict):
                if 'tasks' in json_data and isinstance(json_data['tasks'], list):
                    logger.info(f"Successfully parsed {len(json_data['tasks'])} tasks")
                    return json_data['tasks']
                elif isinstance(json_data, list):
                    # If the result is directly a list of tasks
                    logger.info(f"Successfully parsed {len(json_data)} tasks from direct list")
                    return json_data
            elif json_data and isinstance(json_data, list):
                # Handle case where json_data is directly a list
                logger.info(f"Successfully parsed {len(json_data)} tasks from direct list")
                return json_data

            # If no valid JSON found, check if it's a partial response and return empty
            if result and len(result.strip()) > 5:
                # Check if it looks like a partial JSON response
                if '"emails"' in result or '"tasks"' in result or '"events"' in result:
                    logger.warning(f"Detected partial JSON response, returning empty list. Response: {result}")
                    return []

            # If all parsing strategies fail, log the result for debugging
            logger.warning(f"Could not parse task generation result as JSON. Result preview: {result[:200]}...")
            return []

        except json.JSONDecodeError as e:
            logger.error(f"JSON parsing error: {e}. Result preview: {result[:200] if result else 'None'}...")
            return []
        except Exception as e:
            logger.error(f"Error parsing task result: {e}")
            return []
    
    def _parse_due_date(self, due_date_str: str) -> Optional[datetime]:
        """
        Parse due date string to datetime object.
        
        Args:
            due_date_str: Due date string from agent
            
        Returns:
            Optional[datetime]: Parsed due date or None
        """
        try:
            if not due_date_str:
                return None
                
            # Try to parse ISO format date
            if 'T' in due_date_str:
                return datetime.fromisoformat(due_date_str.replace('Z', '+00:00'))
            else:
                return datetime.strptime(due_date_str, '%Y-%m-%d')
                
        except (ValueError, TypeError):
            # If parsing fails, default to 1 week from now
            return datetime.now() + timedelta(days=7)
    
    def format_task_for_storage(self, task_data: Dict[str, Any], reference_modal: str, reference_modal_id: str) -> Dict[str, Any]:
        """
        Format task data for database storage.
        
        Args:
            task_data: Raw task data from agent
            reference_modal: Source model type (e.g., "Lead", "Note")
            reference_modal_id: Source model ID
            
        Returns:
            Dict: Formatted task data for database
        """
        due_date = self._parse_due_date(task_data.get("due_date"))
        
        return {
            "title": task_data.get("title", "Untitled Task"),
            "description": task_data.get("description"),
            "priority": task_data.get("priority", "medium"),
            "status": "pending",
            "dueDate": due_date,
            "reference_modal": reference_modal,
            "reference_modalId": reference_modal_id,
            "deleted": False
        }
