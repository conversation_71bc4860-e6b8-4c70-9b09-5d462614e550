"""
CopilotKit Middleware

Provides authentication, CORS, logging, and other middleware functionality
for CopilotKit endpoints.
"""

import logging
import time
from typing import Callable, Optional
from fastapi import Request, Response, HTTPException
from starlette.middleware.base import BaseHTTPMiddleware
from fastapi.middleware.cors import CORSMiddleware
from starlette.responses import JSONResponse

from .config import CopilotKitConfig


logger = logging.getLogger(__name__)


class CopilotKitMiddleware(BaseHTTPMiddleware):
    """Custom middleware for CopilotKit endpoints."""
    
    def __init__(self, app, config: CopilotKitConfig):
        super().__init__(app)
        self.config = config
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process requests to CopilotKit endpoints."""
        start_time = time.time()
        
        # Check if this is a CopilotKit endpoint
        if not request.url.path.startswith(self.config.endpoint_path):
            return await call_next(request)
        
        # Log request
        if self.config.log_actions:
            logger.info(f"CopilotKit request: {request.method} {request.url.path}")
        
        try:
            # Extract auth context from headers
            tenant_id = request.headers.get("X-Tenant-ID")
            user_id = request.headers.get("X-User-ID")

            # Try to extract CoAgent state from CopilotKit request body
            try:
                if request.method == "POST":
                    # Read request body to extract CoAgent state
                    body = await request.body()
                    if body:
                        import json
                        request_data = json.loads(body)

                        # Log for debugging
                        logger.info(f"CopilotKit request keys: {list(request_data.keys())}")

                        # Look for CoAgent state in the request
                        # CopilotKit might send it in different formats
                        state_data = None

                        # Check common locations where state might be
                        if 'state' in request_data:
                            state_data = request_data['state']
                        elif 'agentState' in request_data:
                            state_data = request_data['agentState']
                        elif 'context' in request_data and isinstance(request_data['context'], dict):
                            if 'state' in request_data['context']:
                                state_data = request_data['context']['state']

                        if state_data and isinstance(state_data, dict):
                            from agents.lead_agent.coagent_integration import set_coagent_state
                            set_coagent_state(state_data)
                            logger.info(f"Set CoAgent state: {state_data}")
                        else:
                            logger.info("No CoAgent state found in request")

            except Exception as e:
                logger.warning(f"Failed to extract CoAgent state: {e}")

            # Set context for CopilotKit agent if available
            if tenant_id and user_id:
                from agents.lead_agent.context import set_context
                set_context(tenant_id, user_id)
                logger.info(f"Set CopilotKit context: tenant_id={tenant_id}, user_id={user_id}")

            # Authentication check
            if self.config.require_auth:
                await self._authenticate_request(request)

            # Rate limiting
            if self.config.rate_limit_enabled:
                await self._check_rate_limit(request)

            # Process request
            response = await call_next(request)

            # Clear context after request
            if tenant_id and user_id:
                from agents.lead_agent.context import clear_context
                clear_context()

            # Clear CoAgent state
            try:
                from agents.lead_agent.coagent_integration import clear_coagent_state
                clear_coagent_state()
            except Exception as e:
                logger.warning(f"Failed to clear CoAgent state: {e}")
            
            # Log response
            process_time = time.time() - start_time
            if self.config.log_actions:
                logger.info(f"CopilotKit response: {response.status_code} in {process_time:.3f}s")
            
            return response
            
        except HTTPException as e:
            logger.warning(f"CopilotKit request failed: {e.detail}")
            return JSONResponse(
                status_code=e.status_code,
                content={"error": e.detail}
            )
        except Exception as e:
            logger.error(f"CopilotKit request error: {str(e)}")
            return JSONResponse(
                status_code=500,
                content={"error": "Internal server error"}
            )
    
    async def _authenticate_request(self, request: Request) -> None:
        """Authenticate the request."""
        # Extract authentication token
        auth_header = request.headers.get("Authorization")
        if not auth_header:
            raise HTTPException(status_code=401, detail="Authentication required")
        
        # Validate token (implement your authentication logic here)
        # This is a placeholder - implement actual JWT validation
        if not auth_header.startswith("Bearer "):
            raise HTTPException(status_code=401, detail="Invalid authentication format")
        
        token = auth_header.split(" ")[1]
        if not token:
            raise HTTPException(status_code=401, detail="Missing authentication token")
        
        # TODO: Implement JWT validation
        # For now, we'll skip validation but you should implement proper JWT checking
    
    async def _check_rate_limit(self, request: Request) -> None:
        """Check rate limiting for the request."""
        # Implement rate limiting logic here
        # This is a placeholder - you can use Redis or in-memory storage
        client_ip = request.client.host if request.client else "unknown"
        
        # TODO: Implement actual rate limiting
        # For now, we'll just log the request
        logger.debug(f"Rate limit check for {client_ip}")


def setup_cors_middleware(app, config: CopilotKitConfig) -> None:
    """Setup CORS middleware for CopilotKit."""
    app.add_middleware(
        CORSMiddleware,
        allow_origins=config.cors_origins,
        allow_credentials=True,
        allow_methods=config.cors_methods,
        allow_headers=config.cors_headers,
    )
    logger.info("CORS middleware configured for CopilotKit")


def setup_logging_middleware(app) -> None:
    """Setup logging middleware."""
    @app.middleware("http")
    async def log_requests(request: Request, call_next):
        start_time = time.time()
        response = await call_next(request)
        process_time = time.time() - start_time
        
        logger.info(
            f"{request.method} {request.url.path} - "
            f"{response.status_code} - {process_time:.3f}s"
        )
        return response
