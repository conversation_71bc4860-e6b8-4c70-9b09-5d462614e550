generator client {
  provider = "prisma-client-js"
}

generator py {
  provider = "prisma-client-py"
}

datasource db {
  provider = "mongodb"
  url      = env("DATABASE_URL")
}

// ==========================================
// Base Models
// ==========================================

model Session {
  id           String   @id @default(auto()) @map("_id") @db.ObjectId
  sessionToken String   @unique
  userId       String   @db.ObjectId
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id                             String                   @id @default(auto()) @map("_id") @db.ObjectId
  name                           String?
  email                          String?                  @unique
  emailVerified                  DateTime?
  password                       String?
  image                          String?
  role                           UserRole                 @default(USER)
  lastLogin                      DateTime?
  notificationPreferences        String? // JSON string for notification preferences
  createdAt                      DateTime                 @default(now())
  updatedAt                      DateTime                 @updatedAt
  authAccounts                   AuthAccount[]            @relation("AuthAccountUser")
  sessions                       Session[]
  membership                     MemberShip[]
  leads                          Lead[]
  opportunities                  Opportunity[]
  contacts                       Contact[]
  tasks                          Task[]
  events                         Event[]
  businessAccounts               BusinessAccount[]        @relation("UserBusinessAccounts")
  activityLogs                   ActivityLog[]
  campaigns                      Campaign[]
  createdTenants                 Tenant[]                 @relation("CreatedBy")
  updatedTenants                 Tenant[]                 @relation("UpdatedBy")
  createdLeads                   Lead[]                   @relation("LeadCreatedBy")
  updatedLeads                   Lead[]                   @relation("LeadUpdatedBy")
  createdOpportunities           Opportunity[]            @relation("OpportunityCreatedBy")
  updatedOpportunities           Opportunity[]            @relation("OpportunityUpdatedBy")
  createdContacts                Contact[]                @relation("ContactCreatedBy")
  updatedContacts                Contact[]                @relation("ContactUpdatedBy")
  createdTasks                   Task[]                   @relation("TaskCreatedBy")
  updatedTasks                   Task[]                   @relation("TaskUpdatedBy")
  createdEvents                  Event[]                  @relation("EventCreatedBy")
  updatedEvents                  Event[]                  @relation("EventUpdatedBy")
  createdBusinessAccounts        BusinessAccount[]        @relation("BusinessAccountCreatedBy")
  updatedBusinessAccounts        BusinessAccount[]        @relation("BusinessAccountUpdatedBy")
  createdPipelineStages          PipelineStage[]          @relation("PipelineStageCreatedBy")
  updatedPipelineStages          PipelineStage[]          @relation("PipelineStageUpdatedBy")
  createdProducts                Product[]                @relation("ProductCreatedBy")
  updatedProducts                Product[]                @relation("ProductUpdatedBy")
  createdOpportunityProducts     OpportunityProduct[]     @relation("OpportunityProductCreatedBy")
  updatedOpportunityProducts     OpportunityProduct[]     @relation("OpportunityProductUpdatedBy")
  createdSupportTickets          SupportTicket[]          @relation("SupportTicketCreatedBy")
  lastUpdatedSupportTickets      SupportTicket[]          @relation("SupportTicketUpdatedBy")
  assignedSupportTickets         SupportTicket[]          @relation("TicketAssignee")
  createdCampaigns               Campaign[]               @relation("CampaignCreatedBy")
  updatedCampaigns               Campaign[]               @relation("CampaignUpdatedBy")
  createdStatusOptions           StatusOption[]           @relation("StatusOptionCreatedBy")
  updatedStatusOptions           StatusOption[]           @relation("StatusOptionUpdatedBy")
  createdPipelineStageConfigs    PipelineStageConfig[]    @relation("PipelineStageConfigCreatedBy")
  updatedPipelineStageConfigs    PipelineStageConfig[]    @relation("PipelineStageConfigUpdatedBy")
  dashboardLayouts               DashboardLayout[]
  createdDashboardLayouts        DashboardLayout[]        @relation("DashboardLayoutCreatedBy")
  updatedDashboardLayouts        DashboardLayout[]        @relation("DashboardLayoutUpdatedBy")
  createdDashboardWidgets        DashboardWidget[]        @relation("DashboardWidgetCreatedBy")
  updatedDashboardWidgets        DashboardWidget[]        @relation("DashboardWidgetUpdatedBy")
  createdCustomFields            CustomField[]            @relation("CustomFieldCreatedBy")
  updatedCustomFields            CustomField[]            @relation("CustomFieldUpdatedBy")
  createdCustomFieldValues       CustomFieldValue[]       @relation("CustomFieldValueCreatedBy")
  updatedCustomFieldValues       CustomFieldValue[]       @relation("CustomFieldValueUpdatedBy")
  createdFieldPermissions        FieldPermission[]        @relation("FieldPermissionCreatedBy")
  updatedFieldPermissions        FieldPermission[]        @relation("FieldPermissionUpdatedBy")
  createdDefaultFieldPermissions DefaultFieldPermission[] @relation("DefaultFieldPermissionCreatedBy")
  updatedDefaultFieldPermissions DefaultFieldPermission[] @relation("DefaultFieldPermissionUpdatedBy")
  createdInboxes                 Inbox[]                  @relation("InboxCreatedBy")
  updatedInboxes                 Inbox[]                  @relation("InboxUpdatedBy")
  createdConversations           Conversation[]           @relation("ConversationCreatedBy")
  updatedConversations           Conversation[]           @relation("ConversationUpdatedBy")
  assignedConversations          Conversation[]           @relation("AssignedConversations")
  sentMessages                   Message[]                @relation("SentMessages")
  participations                 Participant[]
  createdLabels                  Label[]                  @relation("LabelCreatedBy")
  updatedLabels                  Label[]                  @relation("LabelUpdatedBy")
  inboxAgents                    InboxAgent[]
  createdFeedback                Feedback[]               @relation("FeedbackCreatedBy")
  updatedFeedback                Feedback[]               @relation("FeedbackUpdatedBy")
  feedbackVotes                  FeedbackVote[]           @relation("UserFeedbackVotes")
  feedbackComments               FeedbackComment[]        @relation("UserFeedbackComments")
  notes                          Note[]
  uploadedTicketAttachments      TicketAttachment[]       @relation("TicketAttachmentUploader")
  SupportTicket                  SupportTicket[]
  TicketAttachment               TicketAttachment[]

  // Quote Relations
  ownedQuotes   Quote[] @relation("QuoteOwner")
  createdQuotes Quote[] @relation("QuoteCreatedBy")
  updatedQuotes Quote[] @relation("QuoteUpdatedBy")

  // Approval Workflow Relations
  requestedApprovals   TicketApproval[] @relation("ApprovalRequester")
  reviewedApprovals    TicketApproval[] @relation("ApprovalReviewer")
  assignedRoadmapItems RoadmapItem[]    @relation("RoadmapAssignee")
  assignedTaskItems    TaskItem[]       @relation("TaskItemAssignee")
}

model VerificationToken {
  id         String   @id @default(auto()) @map("_id") @db.ObjectId
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model AuthAccount {
  id                String  @id @default(auto()) @map("_id") @db.ObjectId
  userId            String  @db.ObjectId
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  user              User    @relation("AuthAccountUser", fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("Account") // Maps to the existing Account table in the database
}

// Models for configurable business logic
model StatusOption {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  type        String
  value       String
  label       String
  description String?
  color       String?
  icon        String?
  order       Int      @default(0)
  isDefault   Boolean  @default(false)
  isSystem    Boolean  @default(false)
  tenantId    String   @db.ObjectId
  createdById String?  @db.ObjectId
  updatedById String?  @db.ObjectId
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  tenant    Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdBy User?  @relation("StatusOptionCreatedBy", fields: [createdById], references: [id], onDelete: SetNull)
  updatedBy User?  @relation("StatusOptionUpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)

  @@unique([tenantId, type, value])
  @@index([type])
}

model PipelineStageConfig {
  id          String           @id @default(auto()) @map("_id") @db.ObjectId
  stage       OpportunityStage
  label       String
  description String?
  color       String?
  icon        String?
  order       Int              @default(0)
  tenantId    String           @db.ObjectId
  createdById String?          @db.ObjectId
  updatedById String?          @db.ObjectId
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt

  // Relations
  tenant    Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdBy User?  @relation("PipelineStageConfigCreatedBy", fields: [createdById], references: [id], onDelete: SetNull)
  updatedBy User?  @relation("PipelineStageConfigUpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)

  @@unique([tenantId, stage])
}

// Models for dashboard configuration
model DashboardLayout {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  name        String
  description String?
  isDefault   Boolean  @default(false)
  isSystem    Boolean  @default(false)
  userId      String?  @db.ObjectId
  tenantId    String   @db.ObjectId
  createdById String?  @db.ObjectId
  updatedById String?  @db.ObjectId
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  user      User?             @relation(fields: [userId], references: [id], onDelete: SetNull)
  tenant    Tenant            @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdBy User?             @relation("DashboardLayoutCreatedBy", fields: [createdById], references: [id], onDelete: SetNull)
  updatedBy User?             @relation("DashboardLayoutUpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)
  widgets   DashboardWidget[]

  @@unique([tenantId, userId, name])
}

model DashboardWidget {
  id                String   @id @default(auto()) @map("_id") @db.ObjectId
  type              String
  title             String
  description       String?
  icon              String?
  size              String
  dataSource        String
  config            Json?
  order             Int      @default(0)
  dashboardLayoutId String   @db.ObjectId
  createdById       String?  @db.ObjectId
  updatedById       String?  @db.ObjectId
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Relations
  dashboardLayout DashboardLayout @relation(fields: [dashboardLayoutId], references: [id], onDelete: Cascade)
  createdBy       User?           @relation("DashboardWidgetCreatedBy", fields: [createdById], references: [id], onDelete: SetNull)
  updatedBy       User?           @relation("DashboardWidgetUpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)
}

model Tenant {
  id       String  @id @default(auto()) @map("_id") @db.ObjectId
  name     String
  domain   String?
  logo     String?
  slug     String?
  timezone String?

  // Organization Details for Quotations
  businessName String?
  gstin        String?
  address      String?
  city         String?
  postalCode   String?
  state        String?
  country      String?

  createdById String?  @db.ObjectId
  updatedById String?  @db.ObjectId
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  createdBy               User?                    @relation("CreatedBy", fields: [createdById], references: [id], onDelete: SetNull)
  updatedBy               User?                    @relation("UpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)
  users                   MemberShip[]
  contacts                Contact[]
  events                  Event[]
  leads                   Lead[]
  opportunities           Opportunity[]
  tasks                   Task[]
  businessAccounts        BusinessAccount[]        @relation("OrganizationBusinessAccounts")
  pipelineStages          PipelineStage[]
  products                Product[]
  activityLogs            ActivityLog[]
  supportTickets          SupportTicket[]
  campaigns               Campaign[]
  statusOptions           StatusOption[]
  pipelineStageConfigs    PipelineStageConfig[]
  dashboardLayouts        DashboardLayout[]
  customFields            CustomField[]
  fieldPermissions        FieldPermission[]
  defaultFieldPermissions DefaultFieldPermission[]
  inboxes                 Inbox[]
  conversations           Conversation[]
  messages                Message[]
  labels                  Label[]
  feedback                Feedback[]
  notes                   Note[]
  TicketAttachment        TicketAttachment[]

  // Quote Relations
  quotes              Quote[]
  quoteLines          QuoteLine[]
  opportunityProducts OpportunityProduct[]
  TicketApproval      TicketApproval[]
  RoadmapItem         RoadmapItem[]
  EmailDraft          EmailDraft[]
  TaskItem            TaskItem[]
  EventItem           EventItem[]
}

model MemberShip {
  id        String               @id @default(auto()) @map("_id") @db.ObjectId
  userId    String               @db.ObjectId
  tenantId  String               @db.ObjectId
  role      OrganizationUserRole @default(MEMBER)
  isDefault Boolean              @default(false)
  createdAt DateTime             @default(now())
  updatedAt DateTime             @updatedAt

  // Relations
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  tenant Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@unique([userId, tenantId])
}

model Lead {
  id          String  @id @default(auto()) @map("_id") @db.ObjectId
  // Personal Information
  salutation  String?
  firstName   String?
  lastName    String?
  title       String? // Job Title/Position
  company     String?
  website     String?
  description String?

  // Contact Information
  email           String?
  phone           String?
  additionalPhone String?
  country         String?
  streetAddress   String?
  city            String?
  postalCode      String?
  state           String? // State/Province

  // Business Information
  numberOfEmployees Int?
  companySize       String? // Company size range (e.g., "0-10 employees", "11-20 employees", "50+ employees")
  annualRevenue     String?
  industry          String?
  source            String?

  // System fields
  status             String       @default("OPEN") // Lead Status: OPEN, CONVERTED, LOST
  stage              String       @default("NEW") // Lead Stage: NEW, CONTACTED, QUALIFIED, NURTURING, UNQUALIFIED
  notes              String?
  leadScore          Int?
  tags               String? // JSON array stored as string
  priority           LeadPriority @default(MEDIUM)
  isB2B              Boolean      @default(false)
  isConverted        Boolean      @default(false)
  deleted            Boolean      @default(false) // Soft delete flag - false when active, true when deleted
  deletedAt          DateTime? // Soft delete field - null when active, DateTime when deleted
  convertedAccountId String?      @db.ObjectId
  convertedContactId String?      @db.ObjectId
  userId             String?      @db.ObjectId
  tenantId           String       @db.ObjectId
  campaignId         String?      @db.ObjectId
  createdById        String?      @db.ObjectId
  updatedById        String?      @db.ObjectId
  createdAt          DateTime     @default(now())
  updatedAt          DateTime     @updatedAt

  // Relations
  user                   User?              @relation(fields: [userId], references: [id], onDelete: SetNull)
  tenant                 Tenant             @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  campaign               Campaign?          @relation("CampaignLeads", fields: [campaignId], references: [id], onDelete: SetNull)
  convertedAccount       BusinessAccount?   @relation("LeadConvertedAccount", fields: [convertedAccountId], references: [id], onDelete: SetNull)
  convertedContact       Contact?           @relation("LeadConvertedContact", fields: [convertedContactId], references: [id], onDelete: SetNull)
  createdBy              User?              @relation("LeadCreatedBy", fields: [createdById], references: [id], onDelete: SetNull)
  updatedBy              User?              @relation("LeadUpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)
  convertedOpportunities Opportunity[]      @relation("LeadConvertedOpportunity")
  customFieldValues      CustomFieldValue[] @relation("LeadCustomFields")
  contacts               Contact[]          @relation("ContactFromLead")
}

model Opportunity {
  id                  String            @id @default(auto()) @map("_id") @db.ObjectId
  dealId              String? // Human-readable opportunity ID
  dealName            String // Human-readable title (mapped from name)
  description         String? // Long text description
  contactId           String?           @db.ObjectId // Primary person involved
  accountId           String?           @db.ObjectId // Organization or household
  relatedToType       RelatedToType     @default(LEAD) // Lead, Contact, Account
  relatedToId         String            @db.ObjectId // Foreign key
  associatedContacts  String? // JSON array of contact IDs
  dealOwner           String            @db.ObjectId // Sales rep (mapped from userId)
  stage               OpportunityStage  @default(DISCOVERY) // Sales stage
  status              OpportunityStatus @default(OPEN) // Open, Closed-Won, Closed-Lost
  value               Float? // Estimated value (mapped from value)
  currency            Currency          @default(USD) // USD, CHF, etc.
  expectedCloseDate   DateTime? // Forecast (mapped from closeDate)
  actualCloseDate     DateTime? // Final date of close
  source              String? // Inbound, Referral, etc.
  campaignId          String?           @db.ObjectId // Ties to Campaign object
  tags                String? // JSON array of tags
  createdById         String?           @db.ObjectId // Who created it
  createdAt           DateTime          @default(now()) // Timestamp
  updatedAt           DateTime          @updatedAt // Auto-updated
  convertedFromLeadId String?           @db.ObjectId // If opportunity started from a lead
  convertedAt         DateTime? // When it was created from lead
  lastContactedAt     DateTime? // Most recent interaction
  stageEnteredAt      DateTime? // Timestamp of when current stage was set
  probability         Int?              @default(0) // Forecasting weight
  bookingId           String?           @db.ObjectId // Booking record
  fulfillmentId       String?           @db.ObjectId // Fulfillment record

  // Legacy fields for backward compatibility
  name            String // Mapped to dealName
  notes           String? // Additional notes
  deleted         Boolean   @default(false) // Soft delete flag - false when active, true when deleted
  deletedAt       DateTime? // Soft delete field - null when active, DateTime when deleted
  userId          String?   @db.ObjectId // Mapped to dealOwner
  tenantId        String    @db.ObjectId
  pipelineStageId String?   @db.ObjectId
  updatedById     String?   @db.ObjectId

  // Relations
  user              User?                @relation(fields: [userId], references: [id], onDelete: SetNull)
  tenant            Tenant               @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  account           BusinessAccount?     @relation("BusinessAccountOpportunities", fields: [accountId], references: [id], onDelete: SetNull)
  contact           Contact?             @relation("ContactOpportunities", fields: [contactId], references: [id], onDelete: SetNull)
  pipelineStage     PipelineStage?       @relation(fields: [pipelineStageId], references: [id], onDelete: SetNull)
  createdBy         User?                @relation("OpportunityCreatedBy", fields: [createdById], references: [id], onDelete: SetNull)
  updatedBy         User?                @relation("OpportunityUpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)
  campaign          Campaign?            @relation("CampaignOpportunities", fields: [campaignId], references: [id], onDelete: SetNull)
  convertedFromLead Lead?                @relation("LeadConvertedOpportunity", fields: [convertedFromLeadId], references: [id], onDelete: SetNull)
  products          OpportunityProduct[]
  customFieldValues CustomFieldValue[]   @relation("OpportunityCustomFields")
  CustomFieldValue  CustomFieldValue[]
  quotes            Quote[]
}

model Contact {
  id              String    @id @default(auto()) @map("_id") @db.ObjectId
  firstName       String
  middleName      String?
  lastName        String
  email           String
  phoneNumber     String
  additionalPhone String?
  role            String?
  tags            String? // JSON array stored as string
  source          String?
  description     String?
  deleted         Boolean   @default(false) // Soft delete flag - false when active, true when deleted
  deletedAt       DateTime? // Soft delete field - null when active, DateTime when deleted
  userId          String?   @db.ObjectId // contact_owner
  tenantId        String    @db.ObjectId
  accountId       String?   @db.ObjectId
  campaignId      String?   @db.ObjectId
  leadId          String?   @db.ObjectId
  createdById     String?   @db.ObjectId
  updatedById     String?   @db.ObjectId
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  // Relations
  user                  User?              @relation(fields: [userId], references: [id], onDelete: SetNull)
  tenant                Tenant             @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  account               BusinessAccount?   @relation("BusinessAccountContacts", fields: [accountId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  campaign              Campaign?          @relation("CampaignContacts", fields: [campaignId], references: [id], onDelete: SetNull)
  lead                  Lead?              @relation("ContactFromLead", fields: [leadId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  createdBy             User?              @relation("ContactCreatedBy", fields: [createdById], references: [id], onDelete: SetNull)
  updatedBy             User?              @relation("ContactUpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)
  opportunities         Opportunity[]      @relation("ContactOpportunities")
  supportTickets        SupportTicket[]    @relation("ContactSupportTickets")
  convertedFromLead     Lead[]             @relation("LeadConvertedContact")
  conversations         Conversation[]     @relation("ContactConversations")
  customFieldValues     CustomFieldValue[] @relation("ContactCustomFields")
  CustomFieldValue      CustomFieldValue[]
  feedback              Feedback[]         @relation("ContactFeedback")
  Campaign              Campaign?          @relation(fields: [campaignId], references: [id])
  primaryAccountContact BusinessAccount[]  @relation("AccountPrimaryContact")
  quotes                Quote[]
}

model Task {
  id                String       @id @default(auto()) @map("_id") @db.ObjectId
  title             String
  description       String?
  status            TaskStatus   @default(PENDING)
  priority          TaskPriority @default(MEDIUM)
  startDate         DateTime?    @default(now())
  dueDate           DateTime?
  reference_modal   String // To store the model name (e.g., "Lead", "Deal")
  reference_modalId String // To store the referenced model's ID
  deleted           Boolean      @default(false) // Soft delete flag - false when active, true when deleted
  deletedAt         DateTime? // Soft delete field - null when active, DateTime when deleted
  createdAt         DateTime     @default(now())
  updatedAt         DateTime     @updatedAt

  // Relations
  userId            String?            @db.ObjectId // Assigned to
  tenantId          String             @db.ObjectId
  createdById       String?            @db.ObjectId
  updatedById       String?            @db.ObjectId
  user              User?              @relation(fields: [userId], references: [id], onDelete: SetNull)
  tenant            Tenant             @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdBy         User?              @relation("TaskCreatedBy", fields: [createdById], references: [id], onDelete: SetNull)
  updatedBy         User?              @relation("TaskUpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)
  customFieldValues CustomFieldValue[] @relation("TaskCustomFields")
  CustomFieldValue  CustomFieldValue[]

  @@index([tenantId])
  @@index([userId])
  @@index([reference_modalId])
}

model Event {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  title       String
  description String?
  startDate   DateTime
  endDate     DateTime
  location    String?
  userId      String?  @db.ObjectId
  tenantId    String   @db.ObjectId
  createdById String?  @db.ObjectId
  updatedById String?  @db.ObjectId
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  user      User?  @relation(fields: [userId], references: [id], onDelete: SetNull)
  tenant    Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdBy User?  @relation("EventCreatedBy", fields: [createdById], references: [id], onDelete: SetNull)
  updatedBy User?  @relation("EventUpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)
}

model BusinessAccount {
  id               String      @id @default(auto()) @map("_id") @db.ObjectId
  name             String // account_name
  accountType      AccountType @default(B2B) // account_type (B2B, B2C, Family, Corporate)
  industry         String? // industry (for B2B)
  region           String? // geographic area
  website          String?
  phone            String?
  email            String?
  emailDomain      String? // email_domain (e.g., "@powderbyrne.com")
  employees        Int?
  annualRevenue    String?
  billingAddress   String?
  shippingAddress  String?
  description      String? // context/background
  tags             String? // JSON array stored as string (VIP Client, Strategic Partner, etc.)
  primaryContactId String?     @db.ObjectId // primary_contact_id
  type             AccountType @default(CUSTOMER) // keeping for backward compatibility
  deleted          Boolean     @default(false) // Soft delete flag - false when active, true when deleted
  deletedAt        DateTime? // Soft delete field - null when active, DateTime when deleted
  userId           String?     @db.ObjectId // account_owner
  tenantId         String      @db.ObjectId
  createdById      String?     @db.ObjectId
  updatedById      String?     @db.ObjectId
  createdAt        DateTime    @default(now()) // auto-generated
  updatedAt        DateTime    @updatedAt // auto-updated

  // Relations
  user              User?              @relation("UserBusinessAccounts", fields: [userId], references: [id], onDelete: SetNull)
  tenant            Tenant             @relation("OrganizationBusinessAccounts", fields: [tenantId], references: [id], onDelete: Cascade)
  primaryContact    Contact?           @relation("AccountPrimaryContact", fields: [primaryContactId], references: [id], onDelete: SetNull, onUpdate: NoAction)
  createdBy         User?              @relation("BusinessAccountCreatedBy", fields: [createdById], references: [id], onDelete: SetNull)
  updatedBy         User?              @relation("BusinessAccountUpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)
  contacts          Contact[]          @relation("BusinessAccountContacts")
  opportunities     Opportunity[]      @relation("BusinessAccountOpportunities")
  supportTickets    SupportTicket[]    @relation("BusinessAccountSupportTickets")
  convertedFromLead Lead[]             @relation("LeadConvertedAccount")
  conversations     Conversation[]     @relation("AccountConversations")
  customFieldValues CustomFieldValue[] @relation("AccountCustomFields")
  feedback          Feedback[]         @relation("BusinessAccountFeedback")
  quotes            Quote[]
}

model PipelineStage {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  name        String
  order       Int
  description String?
  tenantId    String   @db.ObjectId
  createdById String?  @db.ObjectId
  updatedById String?  @db.ObjectId
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  tenant            Tenant             @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdBy         User?              @relation("PipelineStageCreatedBy", fields: [createdById], references: [id], onDelete: SetNull)
  updatedBy         User?              @relation("PipelineStageUpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)
  opportunities     Opportunity[]
  customFieldValues CustomFieldValue[] @relation("PipelineStageCustomFields")
  CustomFieldValue  CustomFieldValue[]
}

model Product {
  id          String    @id @default(auto()) @map("_id") @db.ObjectId
  name        String
  description String?
  sku         String?
  price       Float
  cost        Float?
  deleted     Boolean   @default(false) // Soft delete flag - false when active, true when deleted
  deletedAt   DateTime? // Soft delete field - null when active, DateTime when deleted
  tenantId    String    @db.ObjectId
  createdById String?   @db.ObjectId
  updatedById String?   @db.ObjectId
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  tenant              Tenant               @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdBy           User?                @relation("ProductCreatedBy", fields: [createdById], references: [id], onDelete: SetNull)
  updatedBy           User?                @relation("ProductUpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)
  opportunityProducts OpportunityProduct[]
  quoteLines          QuoteLine[]
}

model OpportunityProduct {
  id                   String                @id @default(auto()) @map("_id") @db.ObjectId
  quantity             Int
  unitPrice            Float
  discount             Float?                @default(0)
  totalPrice           Float
  customerBudget       Float? // Customer budget for this line item
  taxRate              Float?                @default(0) // Tax rate percentage
  description          String? // Additional description for this line item
  status               OpportunityLineStatus @default(ACTIVE) // Line item status
  expectedDeliveryDate DateTime? // Expected delivery date
  notes                String? // Notes/comments for this line item
  opportunityId        String                @db.ObjectId
  productId            String                @db.ObjectId
  tenantId             String                @db.ObjectId
  createdById          String?               @db.ObjectId
  updatedById          String?               @db.ObjectId
  createdAt            DateTime              @default(now())
  updatedAt            DateTime              @updatedAt

  // Relations
  opportunity Opportunity @relation(fields: [opportunityId], references: [id], onDelete: Cascade)
  product     Product     @relation(fields: [productId], references: [id], onDelete: Cascade)
  tenant      Tenant      @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdBy   User?       @relation("OpportunityProductCreatedBy", fields: [createdById], references: [id], onDelete: SetNull)
  updatedBy   User?       @relation("OpportunityProductUpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)

  @@index([opportunityId])
  @@index([productId])
  @@index([tenantId])
}

model SupportTicket {
  id              String                @id @default(auto()) @map("_id") @db.ObjectId
  ticketNumber    String // Human-readable ticket number (e.g., TCK-000238)
  subject         String // Changed from title to subject
  description     String?
  status          SupportTicketStatus   @default(OPEN)
  priority        SupportTicketPriority @default(MEDIUM)
  type            TicketType?
  tags            String[] // Array of tags
  channel         TicketChannel         @default(EMAIL)
  language        String?
  creationSource  TicketCreationSource  @default(MANUAL)
  sourceSessionId String?               @db.ObjectId // ID of the originating inbox/chat session

  // Core relationships
  contactId  String  @db.ObjectId // Made required
  companyId  String? @db.ObjectId // Reference to company/account (renamed from accountId)
  assigneeId String? @db.ObjectId // Agent assigned to this ticket
  teamId     String? @db.ObjectId // Team responsible (using tenantId as team for now)
  tenantId   String  @db.ObjectId

  // Audit fields
  createdById     String @db.ObjectId // Made required (who created the ticket)
  lastUpdatedById String @db.ObjectId // Last user who updated the ticket

  // Timestamps
  createdAt        DateTime  @default(now())
  updatedAt        DateTime  @updatedAt
  closedAt         DateTime?
  firstResponseAt  DateTime?
  lastUserReplyAt  DateTime?
  lastAgentReplyAt DateTime?
  assignedAt       DateTime?
  resolvedAt       DateTime?

  // Resolution and AI fields
  resolutionType    TicketResolutionType?
  aiConfidenceScore Float? // 0 to 1
  aiModelVersion    String?

  // SLA and tracking
  slaViolation  Boolean  @default(false)
  internalNotes String[] // Array of internal-only notes

  // External integration
  externalRefId String? // Optional external system reference
  deleted       Boolean   @default(false) // Soft delete flag - false when active, true when deleted
  deletedAt     DateTime? // Soft delete field - null when active, DateTime when deleted
  auditLogId    String?   @db.ObjectId // Reference to audit log entry

  // Relations
  contact       Contact          @relation("ContactSupportTickets", fields: [contactId], references: [id], onDelete: Cascade)
  company       BusinessAccount? @relation("BusinessAccountSupportTickets", fields: [companyId], references: [id], onDelete: SetNull)
  assignee      User?            @relation("TicketAssignee", fields: [assigneeId], references: [id], onDelete: SetNull)
  tenant        Tenant           @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdBy     User             @relation("SupportTicketCreatedBy", fields: [createdById], references: [id], onDelete: Cascade)
  lastUpdatedBy User             @relation("SupportTicketUpdatedBy", fields: [lastUpdatedById], references: [id], onDelete: Cascade)

  // New relations
  conversation      Conversation?      @relation(fields: [conversationId], references: [id])
  conversationId    String?            @db.ObjectId
  attachments       TicketAttachment[]
  customFieldValues CustomFieldValue[] @relation("TicketCustomFields")
  User              User?              @relation(fields: [userId], references: [id])
  userId            String?            @db.ObjectId
  CustomFieldValue  CustomFieldValue[]

  // Approval Workflow Relations
  approvals    TicketApproval[]
  roadmapItems RoadmapItem[]

  @@index([tenantId])
  @@index([contactId])
  @@index([assigneeId])
  @@index([status])
  @@index([priority])
  @@index([createdAt])
  @@index([conversationId])
}

// Ticket Attachment model - Represents file attachments
model TicketAttachment {
  id           String   @id @default(auto()) @map("_id") @db.ObjectId
  fileName     String
  originalName String
  fileSize     Int
  mimeType     String
  fileUrl      String
  isPublic     Boolean  @default(false) // Whether customer can see this attachment
  uploadedAt   DateTime @default(now())
  ticketId     String   @db.ObjectId
  uploadedById String   @db.ObjectId
  tenantId     String   @db.ObjectId

  // Relations
  ticket     SupportTicket @relation(fields: [ticketId], references: [id], onDelete: Cascade)
  uploadedBy User          @relation("TicketAttachmentUploader", fields: [uploadedById], references: [id], onDelete: Cascade)
  tenant     Tenant        @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  User       User?         @relation(fields: [userId], references: [id])
  userId     String?       @db.ObjectId

  @@index([ticketId])
}

model Campaign {
  id             String         @id @default(auto()) @map("_id") @db.ObjectId
  name           String
  type           CampaignType   @default(EMAIL)
  description    String?
  status         CampaignStatus @default(DRAFT)
  startDate      DateTime?      @default(now())
  endDate        DateTime?
  budget         Float?
  targetAudience String?
  deleted        Boolean        @default(false) // Soft delete flag - false when active, true when deleted
  deletedAt      DateTime? // Soft delete field - null when active, DateTime when deleted
  userId         String?        @db.ObjectId
  tenantId       String         @db.ObjectId
  createdById    String?        @db.ObjectId
  updatedById    String?        @db.ObjectId
  createdAt      DateTime       @default(now())
  updatedAt      DateTime       @updatedAt

  // Relations
  user              User?              @relation(fields: [userId], references: [id], onDelete: SetNull)
  tenant            Tenant             @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdBy         User?              @relation("CampaignCreatedBy", fields: [createdById], references: [id], onDelete: SetNull)
  updatedBy         User?              @relation("CampaignUpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)
  leads             Lead[]             @relation("CampaignLeads")
  opportunities     Opportunity[]      @relation("CampaignOpportunities")
  contacts          Contact[]          @relation("CampaignContacts")
  customFieldValues CustomFieldValue[] @relation("CampaignCustomFields")
  CustomFieldValue  CustomFieldValue[]
  Contact           Contact[]
}

model CustomField {
  id                  String          @id @default(auto()) @map("_id") @db.ObjectId
  name                String // Internal field name (e.g., "custom_field_1")
  label               String // Display label (e.g., "Annual Revenue")
  description         String?
  type                CustomFieldType
  isRequired          Boolean         @default(false)
  defaultValue        String?
  placeholder         String?
  helpText            String?
  options             Json? // For SELECT and MULTI_SELECT types
  order               Int             @default(0)
  entityType          String // "Lead", "Contact", "Account", "Opportunity"
  isVisibleByDefault  Boolean         @default(true)
  isEditableByDefault Boolean         @default(false)

  // Advanced field properties
  minLength             Int?
  maxLength             Int?
  minValue              Float?
  maxValue              Float?
  decimalPlaces         Int?    @default(2) // For NUMBER, CURRENCY, PERCENT
  formulaExpression     String? // For FORMULA type
  lookupEntityType      String? // For LOOKUP type
  lookupField           String? // For LOOKUP type
  validationRule        String? // Custom validation expression
  validationMessage     String? // Message to show when validation fails
  displayFormat         String? // For DATE, DATETIME, TIME types
  isUnique              Boolean @default(false)
  isSearchable          Boolean @default(true)
  isSortable            Boolean @default(true)
  isFilterable          Boolean @default(true)
  groupName             String? // For grouping fields in sections
  displayOrder          Int     @default(0) // For ordering within groups
  conditionalVisibility Json? // Rules for conditional display
  maskType              String? // For input masking (phone, SSN, etc.)

  createdAt   DateTime           @default(now())
  updatedAt   DateTime           @updatedAt
  tenantId    String             @db.ObjectId
  createdById String?            @db.ObjectId
  updatedById String?            @db.ObjectId
  tenant      Tenant             @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdBy   User?              @relation("CustomFieldCreatedBy", fields: [createdById], references: [id], onDelete: SetNull)
  updatedBy   User?              @relation("CustomFieldUpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)
  values      CustomFieldValue[]
  permissions FieldPermission[]  @relation("FieldPermissions")

  @@unique([tenantId, entityType, name])
  @@index([entityType])
}

model CustomFieldValue {
  id            String           @id @default(auto()) @map("_id") @db.ObjectId
  value         String? // All values stored as strings and converted as needed
  entityId      String // ID of the entity (Lead, Contact, etc.)
  createdAt     DateTime         @default(now())
  updatedAt     DateTime         @updatedAt
  customFieldId String           @db.ObjectId
  createdById   String?          @db.ObjectId
  updatedById   String?          @db.ObjectId
  customField   CustomField      @relation(fields: [customFieldId], references: [id], onDelete: Cascade)
  createdBy     User?            @relation("CustomFieldValueCreatedBy", fields: [createdById], references: [id], onDelete: SetNull)
  updatedBy     User?            @relation("CustomFieldValueUpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)
  leadId        String?          @db.ObjectId
  lead          Lead?            @relation("LeadCustomFields", fields: [leadId], references: [id], onDelete: Cascade)
  accountId     String?          @db.ObjectId
  account       BusinessAccount? @relation("AccountCustomFields", fields: [accountId], references: [id], onDelete: Cascade)
  opportunityId String?          @db.ObjectId
  opportunity   Opportunity?     @relation("OpportunityCustomFields", fields: [opportunityId], references: [id], onDelete: Cascade)
  contactId     String?          @db.ObjectId
  contact       Contact?         @relation("ContactCustomFields", fields: [contactId], references: [id], onDelete: Cascade)
  campaignId    String?          @db.ObjectId
  campaign      Campaign?        @relation("CampaignCustomFields", fields: [campaignId], references: [id], onDelete: Cascade)
  taskId        String?          @db.ObjectId
  task          Task?            @relation("TaskCustomFields", fields: [taskId], references: [id], onDelete: Cascade)

  pipelineStageId String?        @db.ObjectId
  pipelineStage   PipelineStage? @relation("PipelineStageCustomFields", fields: [pipelineStageId], references: [id], onDelete: Cascade)
  supportTicketId String?        @db.ObjectId
  supportTicket   SupportTicket? @relation("TicketCustomFields", fields: [supportTicketId], references: [id], onDelete: Cascade)
  Opportunity     Opportunity?   @relation(fields: [opportunityId], references: [id])
  Contact         Contact?       @relation(fields: [contactId], references: [id])
  Task            Task?          @relation(fields: [taskId], references: [id])
  PipelineStage   PipelineStage? @relation(fields: [pipelineStageId], references: [id])
  SupportTicket   SupportTicket? @relation(fields: [supportTicketId], references: [id])
  Campaign        Campaign?      @relation(fields: [campaignId], references: [id])

  @@unique([customFieldId, entityId])
  @@index([entityId])
}

// ==========================================
// Basic Quote Models (Simplified)
// ==========================================

model Quote {
  id          String  @id @default(auto()) @map("_id") @db.ObjectId
  quoteNumber String  @unique
  name        String
  description String?

  // Status and lifecycle
  status  QuoteStatus @default(DRAFT)
  version Int         @default(1)

  // Relationships
  opportunityId String? @db.ObjectId
  accountId     String? @db.ObjectId
  contactId     String? @db.ObjectId

  // Pricing summary
  subtotal      Float    @default(0)
  totalDiscount Float    @default(0)
  totalTax      Float    @default(0)
  grandTotal    Float    @default(0)
  currency      Currency @default(USD)

  // Terms and conditions
  paymentTerms  String?   @default("Net 30")
  deliveryTerms String?
  validUntil    DateTime?

  // Quotation From fields (Seller/Company details)
  quotationFromCountry      String?
  quotationFromBusinessName String?
  quotationFromGSTIN        String?
  quotationFromAddress      String?
  quotationFromCity         String?
  quotationFromPostalCode   String?
  quotationFromState        String?

  // Quotation To fields (Customer/Buyer details)
  quotationToCountry      String?
  quotationToBusinessName String?
  quotationToGSTIN        String?
  quotationToAddress      String?
  quotationToCity         String?
  quotationToPostalCode   String?
  quotationToState        String?

  // Document generation
  generatedPdfUrl String?
  lastGeneratedAt DateTime?

  // Soft delete fields
  deleted   Boolean   @default(false) // Soft delete flag - false when active, true when deleted
  deletedAt DateTime? // Soft delete field - null when active, DateTime when deleted

  tenantId    String  @db.ObjectId
  ownerId     String  @db.ObjectId
  createdById String? @db.ObjectId
  updatedById String? @db.ObjectId

  // Relations
  opportunity Opportunity?     @relation(fields: [opportunityId], references: [id])
  account     BusinessAccount? @relation(fields: [accountId], references: [id])
  contact     Contact?         @relation(fields: [contactId], references: [id])
  tenant      Tenant           @relation(fields: [tenantId], references: [id])
  owner       User             @relation("QuoteOwner", fields: [ownerId], references: [id])
  createdBy   User?            @relation("QuoteCreatedBy", fields: [createdById], references: [id])
  updatedBy   User?            @relation("QuoteUpdatedBy", fields: [updatedById], references: [id])

  lines QuoteLine[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([tenantId])
  @@index([opportunityId])
  @@index([status])
  @@index([ownerId])
}

model QuoteLine {
  id          String  @id @default(auto()) @map("_id") @db.ObjectId
  lineNumber  Int
  quantity    Int     @default(1)
  unitPrice   Float
  listPrice   Float?
  discount    Float   @default(0)
  lineTotal   Float
  description String?

  quoteId   String  @db.ObjectId
  productId String? @db.ObjectId
  tenantId  String  @db.ObjectId

  // Relations
  quote   Quote    @relation(fields: [quoteId], references: [id], onDelete: Cascade)
  product Product? @relation(fields: [productId], references: [id])
  tenant  Tenant   @relation(fields: [tenantId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([quoteId])
  @@index([productId])
}

model FieldPermission {
  id            String               @id @default(auto()) @map("_id") @db.ObjectId
  customFieldId String               @db.ObjectId
  role          OrganizationUserRole
  canView       Boolean              @default(true)
  canEdit       Boolean              @default(false)
  tenantId      String               @db.ObjectId
  createdById   String?              @db.ObjectId
  updatedById   String?              @db.ObjectId
  createdAt     DateTime             @default(now())
  updatedAt     DateTime             @updatedAt

  // Relations
  customField CustomField @relation("FieldPermissions", fields: [customFieldId], references: [id], onDelete: Cascade)
  tenant      Tenant      @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdBy   User?       @relation("FieldPermissionCreatedBy", fields: [createdById], references: [id], onDelete: SetNull)
  updatedBy   User?       @relation("FieldPermissionUpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)

  @@unique([customFieldId, role, tenantId])
  @@index([role])
  @@index([customFieldId])
}

model DefaultFieldPermission {
  id          String               @id @default(auto()) @map("_id") @db.ObjectId
  entityType  String
  fieldName   String
  role        OrganizationUserRole
  canView     Boolean              @default(true)
  canEdit     Boolean              @default(false)
  tenantId    String               @db.ObjectId
  createdById String?              @db.ObjectId
  updatedById String?              @db.ObjectId
  createdAt   DateTime             @default(now())
  updatedAt   DateTime             @updatedAt

  // Relations
  tenant    Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdBy User?  @relation("DefaultFieldPermissionCreatedBy", fields: [createdById], references: [id], onDelete: SetNull)
  updatedBy User?  @relation("DefaultFieldPermissionUpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)

  @@unique([entityType, fieldName, role, tenantId])
  @@index([role])
  @@index([entityType, fieldName])
}

// Inbox model - Represents a communication channel
model Inbox {
  id          String    @id @default(auto()) @map("_id") @db.ObjectId
  name        String
  description String?
  type        InboxType
  icon        String?
  color       String?
  settings    Json? // Channel-specific settings
  isActive    Boolean   @default(true) // Active status flag - true when active, false when inactive
  deleted     Boolean   @default(false) // Soft delete flag - false when active, true when deleted
  deletedAt   DateTime? // Soft delete field - null when active, DateTime when deleted
  tenantId    String    @db.ObjectId
  createdById String?   @db.ObjectId
  updatedById String?   @db.ObjectId
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  tenant        Tenant         @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdBy     User?          @relation("InboxCreatedBy", fields: [createdById], references: [id], onDelete: SetNull)
  updatedBy     User?          @relation("InboxUpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)
  conversations Conversation[]
  agents        InboxAgent[]
}

// InboxAgent model - Join table for Inbox and User (agents)
model InboxAgent {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  inboxId   String   @db.ObjectId
  userId    String   @db.ObjectId
  role      String   @default("agent")
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  inbox Inbox @relation(fields: [inboxId], references: [id], onDelete: Cascade)
  user  User  @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([inboxId, userId])
  @@index([inboxId])
  @@index([userId])
}

// Conversation model - Represents a thread of messages
model Conversation {
  id            String             @id @default(auto()) @map("_id") @db.ObjectId
  subject       String?
  status        ConversationStatus @default(OPEN)
  priority      Int                @default(0) // 0 = normal, 1 = high, 2 = urgent
  isStarred     Boolean            @default(false)
  lastMessageAt DateTime?
  snoozedUntil  DateTime?
  meta          Json?
  createdAt     DateTime           @default(now())
  updatedAt     DateTime           @updatedAt
  inboxId       String?            @db.ObjectId
  assignedToId  String?            @db.ObjectId
  contactId     String?            @db.ObjectId
  accountId     String?            @db.ObjectId
  tenantId      String             @db.ObjectId
  createdById   String?            @db.ObjectId
  updatedById   String?            @db.ObjectId

  // Relations
  inbox         Inbox?              @relation(fields: [inboxId], references: [id], onDelete: Cascade)
  assignedTo    User?               @relation("AssignedConversations", fields: [assignedToId], references: [id], onDelete: SetNull)
  contact       Contact?            @relation("ContactConversations", fields: [contactId], references: [id], onDelete: SetNull)
  account       BusinessAccount?    @relation("AccountConversations", fields: [accountId], references: [id], onDelete: SetNull)
  tenant        Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdBy     User?               @relation("ConversationCreatedBy", fields: [createdById], references: [id], onDelete: SetNull)
  updatedBy     User?               @relation("ConversationUpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)
  messages      Message[]
  participants  Participant[]
  labels        ConversationLabel[]
  SupportTicket SupportTicket[]
}

model Message {
  id             String        @id @default(auto()) @map("_id") @db.ObjectId
  content        String
  contentType    String        @default("text")
  status         MessageStatus @default(SENT)
  isIncoming     Boolean       @default(false)
  attachments    Json?
  meta           Json?
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt
  conversationId String        @db.ObjectId
  senderId       String?       @db.ObjectId
  tenantId       String        @db.ObjectId

  // Relations
  conversation Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  sender       User?        @relation("SentMessages", fields: [senderId], references: [id], onDelete: SetNull)
  tenant       Tenant       @relation(fields: [tenantId], references: [id], onDelete: Cascade)
}

model Participant {
  id             String    @id @default(auto()) @map("_id") @db.ObjectId
  role           String    @default("member") // member, admin, observer
  joinedAt       DateTime  @default(now())
  lastReadAt     DateTime?
  conversationId String    @db.ObjectId
  userId         String    @db.ObjectId

  // Relations
  conversation Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  user         User         @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([conversationId, userId])
}

// Label model - Tags for organizing conversations
model Label {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  name        String
  description String?
  color       String?
  tenantId    String   @db.ObjectId
  createdById String?  @db.ObjectId
  updatedById String?  @db.ObjectId
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  tenant        Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdBy     User?               @relation("LabelCreatedBy", fields: [createdById], references: [id], onDelete: SetNull)
  updatedBy     User?               @relation("LabelUpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)
  conversations ConversationLabel[]
}

// Join table for Conversation and Label
model ConversationLabel {
  id             String   @id @default(auto()) @map("_id") @db.ObjectId
  conversationId String   @db.ObjectId
  labelId        String   @db.ObjectId
  createdAt      DateTime @default(now())

  // Relations
  conversation Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  label        Label        @relation(fields: [labelId], references: [id], onDelete: Cascade)

  @@unique([conversationId, labelId])
}

// Feedback model - For collecting and managing user feedback
model Feedback {
  id          String           @id @default(auto()) @map("_id") @db.ObjectId
  title       String
  description String
  type        FeedbackType     @default(FEATURE)
  status      FeedbackStatus   @default(SUBMITTED)
  priority    FeedbackPriority @default(MEDIUM)
  isPublic    Boolean          @default(true)
  isArchived  Boolean          @default(false)
  deleted     Boolean          @default(false) // Soft delete flag - false when active, true when deleted
  deletedAt   DateTime? // Soft delete field - null when active, DateTime when deleted
  voteCount   Int              @default(0)
  tags        String? // Comma-separated tags
  tenantId    String           @db.ObjectId
  createdById String?          @db.ObjectId
  updatedById String?          @db.ObjectId
  accountId   String?          @db.ObjectId
  contactId   String?          @db.ObjectId
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt

  // Relations
  tenant    Tenant            @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdBy User?             @relation("FeedbackCreatedBy", fields: [createdById], references: [id], onDelete: SetNull)
  updatedBy User?             @relation("FeedbackUpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)
  account   BusinessAccount?  @relation("BusinessAccountFeedback", fields: [accountId], references: [id], onDelete: SetNull)
  contact   Contact?          @relation("ContactFeedback", fields: [contactId], references: [id], onDelete: SetNull)
  votes     FeedbackVote[]
  comments  FeedbackComment[]

  @@index([type, status, isPublic, isArchived])
  @@index([tenantId])
}

// FeedbackVote model - For tracking votes on feedback items
model FeedbackVote {
  id         String   @id @default(auto()) @map("_id") @db.ObjectId
  feedbackId String   @db.ObjectId
  userId     String   @db.ObjectId
  createdAt  DateTime @default(now())

  // Relations
  feedback Feedback @relation(fields: [feedbackId], references: [id], onDelete: Cascade)
  user     User     @relation("UserFeedbackVotes", fields: [userId], references: [id], onDelete: Cascade)

  @@unique([feedbackId, userId])
  @@index([feedbackId])
}

// FeedbackComment model - For comments on feedback items
model FeedbackComment {
  id         String   @id @default(auto()) @map("_id") @db.ObjectId
  content    String
  isInternal Boolean  @default(false) // If true, only visible to tenant members
  feedbackId String   @db.ObjectId
  userId     String   @db.ObjectId
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  // Relations
  feedback Feedback @relation(fields: [feedbackId], references: [id], onDelete: Cascade)
  user     User     @relation("UserFeedbackComments", fields: [userId], references: [id], onDelete: Cascade)

  @@index([feedbackId])
  @@index([userId])
}

model Note {
  id                String    @id @default(auto()) @map("_id") @db.ObjectId
  title             String
  content           String
  reference_modal   String
  reference_modalId String    @db.ObjectId
  deleted           Boolean   @default(false) // Soft delete flag - false when active, true when deleted
  deletedAt         DateTime? // Soft delete field - null when active, DateTime when deleted
  tenantId          String    @db.ObjectId
  authorId          String    @db.ObjectId
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt

  // Relations
  tenant Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  author User   @relation(fields: [authorId], references: [id], onDelete: Cascade)

  @@index([tenantId])
  @@index([authorId])
  @@index([reference_modalId])
}

model ActivityLog {
  id               String        @id @default(auto()) @map("_id") @db.ObjectId
  title            String
  description      String?
  activity_type    ActivityType?
  activity_type_id String?       @db.ObjectId
  action           ActionType? // CRUD action on the related entity

  related_to_type RelatedToType
  related_to_id   String        @db.ObjectId
  user_id         String?       @db.ObjectId
  tenantId        String        @db.ObjectId

  visibility ActivityVisibility @default(INTERNAL)

  activity_time DateTime @default(now())
  created_at    DateTime @default(now())
  updated_at    DateTime @updatedAt

  // Relations
  user   User?  @relation(fields: [user_id], references: [id], onDelete: SetNull)
  tenant Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@index([related_to_type, related_to_id])
  @@index([user_id])
  @@index([tenantId])
}

// ==========================================
// Approval Workflow Models
// ==========================================

// Ticket Approval model - Tracks approval requests for feature tickets
model TicketApproval {
  id          String         @id @default(auto()) @map("_id") @db.ObjectId
  status      ApprovalStatus @default(PENDING)
  requestedAt DateTime       @default(now())
  reviewedAt  DateTime?
  comments    String? // Approval/rejection comments

  // Relationships
  ticketId    String  @db.ObjectId
  requesterId String  @db.ObjectId // User who requested approval
  reviewerId  String? @db.ObjectId // User who reviewed the approval
  tenantId    String  @db.ObjectId

  // Relations
  ticket    SupportTicket @relation(fields: [ticketId], references: [id], onDelete: Cascade)
  requester User          @relation("ApprovalRequester", fields: [requesterId], references: [id], onDelete: Cascade)
  reviewer  User?         @relation("ApprovalReviewer", fields: [reviewerId], references: [id], onDelete: SetNull)
  tenant    Tenant        @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  // Audit fields
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt
  RoadmapItem RoadmapItem[]

  @@index([ticketId])
  @@index([status])
  @@index([tenantId])
}

// Roadmap Item model - Stores approved feature requests
model RoadmapItem {
  id              String          @id @default(auto()) @map("_id") @db.ObjectId
  title           String
  description     String?
  status          RoadmapStatus   @default(PLANNED)
  priority        RoadmapPriority @default(MEDIUM)
  category        String? // Feature category (e.g., "UI/UX", "API", "Integration")
  estimatedEffort String? // Effort estimation (e.g., "Small", "Medium", "Large")
  targetQuarter   String? // Target quarter for completion (e.g., "Q1 2024")
  targetDate      DateTime? // Specific target date
  completedAt     DateTime? // When the feature was completed

  // Relationships
  originalTicketId String? @db.ObjectId // Reference to the original feature request ticket
  approvalId       String? @db.ObjectId // Reference to the approval that created this roadmap item
  assigneeId       String? @db.ObjectId // Developer/team assigned to this feature
  tenantId         String  @db.ObjectId

  // Relations
  originalTicket SupportTicket?  @relation(fields: [originalTicketId], references: [id], onDelete: SetNull)
  approval       TicketApproval? @relation(fields: [approvalId], references: [id], onDelete: SetNull)
  assignee       User?           @relation("RoadmapAssignee", fields: [assigneeId], references: [id], onDelete: SetNull)
  tenant         Tenant          @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  // Audit fields
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([status])
  @@index([priority])
  @@index([tenantId])
  @@index([originalTicketId])
}

// ==========================================
// Multi-Agent AI Action Models
// ==========================================

model EmailDraft {
  id            String    @id @default(auto()) @map("_id") @db.ObjectId
  from          String?
  to            String[]
  subject       String? /// @encrypted
  htmlContent   String? /// @encrypted
  attachments   Json?
  scheduledTime DateTime?
  status        String    @default("draft") // draft, sent, scheduled, failed
  tenantId      String?   @db.ObjectId
  deleted       Boolean   @default(false)

  // Reference to source (Lead, Note, etc.)
  reference_modal   String // "Lead", "Note", "Opportunity"
  reference_modalId String // ID of the referenced entity

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  tenant Tenant? @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@index([tenantId])
  @@index([reference_modalId])
  @@index([status])
}

model TaskItem {
  id          String    @id @default(auto()) @map("_id") @db.ObjectId
  title       String
  description String?
  priority    String    @default("medium") // low, medium, high, urgent
  status      String    @default("pending") // pending, in_progress, completed, cancelled
  dueDate     DateTime?
  assigneeId  String?   @db.ObjectId
  tenantId    String?   @db.ObjectId
  deleted     Boolean   @default(false)

  // Reference to source
  reference_modal   String
  reference_modalId String

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  tenant   Tenant? @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  assignee User?   @relation("TaskItemAssignee", fields: [assigneeId], references: [id], onDelete: SetNull)

  @@index([tenantId])
  @@index([assigneeId])
  @@index([reference_modalId])
  @@index([status])
}

model EventItem {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  title       String
  description String?
  startTime   DateTime
  endTime     DateTime
  location    String?
  attendees   String[]
  eventType   String   @default("meeting") // meeting, call, reminder, deadline
  status      String   @default("scheduled") // scheduled, completed, cancelled
  tenantId    String?  @db.ObjectId
  deleted     Boolean  @default(false)

  // Reference to source
  reference_modal   String
  reference_modalId String

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  tenant Tenant? @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@index([tenantId])
  @@index([reference_modalId])
  @@index([status])
  @@index([startTime])
}

// ==========================================
// Enums
// ==========================================

enum TaskPriority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

enum UserRole {
  USER
  ADMIN
  SUPER_ADMIN
}

enum OrganizationUserRole {
  OWNER
  ADMIN
  MEMBER
}

enum AccountType {
  B2B
  B2C
  FAMILY
  CORPORATE
  CUSTOMER
  PROSPECT
  PARTNER
  VENDOR
  OTHER
}

enum ActivityType {
  CALL
  EMAIL
  NOTE
  MEETING
  WHATSAPP
  TASK
  OTHER
}

enum SupportTicketStatus {
  OPEN
  IN_PROGRESS
  WAITING_ON_CUSTOMER
  WAITING_ON_THIRD_PARTY
  RESOLVED
  CLOSED
}

enum SupportTicketPriority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

enum CampaignType {
  EMAIL
  SMS
  SOCIAL_MEDIA
  DIRECT_MAIL
  WEBINAR
  EVENT
}

enum CampaignStatus {
  DRAFT
  SCHEDULED
  ACTIVE
  PAUSED
  COMPLETED
  CANCELLED
}

enum FeedbackStatus {
  SUBMITTED
  UNDER_REVIEW
  PLANNED
  IN_PROGRESS
  COMPLETED
  DECLINED
}

enum FeedbackType {
  BUG
  FEATURE
  IMPROVEMENT
  QUESTION
  OTHER
}

enum FeedbackPriority {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum InboxType {
  EMAIL
  CHAT
  WHATSAPP
  SMS
  TWITTER
  FACEBOOK
  INSTAGRAM
  CUSTOM
}

enum ConversationStatus {
  OPEN
  RESOLVED
  PENDING
  SNOOZED
}

enum MessageStatus {
  SENT
  DELIVERED
  READ
  FAILED
}

enum CustomFieldType {
  TEXT
  TEXTAREA
  RICH_TEXT
  NUMBER
  CURRENCY
  PERCENT
  DATE
  DATETIME
  TIME
  BOOLEAN
  CHECKBOX
  SELECT
  MULTI_SELECT
  PICKLIST
  URL
  EMAIL
  PHONE
  FORMULA
  LOOKUP
  GEOLOCATION
  FILE
  IMAGE
}

enum OpportunityStage {
  DISCOVERY
  PROPOSAL_SENT
  NEGOTIATION
  CONTRACT_SENT
  CLOSED_WON
  CLOSED_LOST
}

enum OpportunityStatus {
  OPEN
  CLOSED_WON
  CLOSED_LOST
}

enum RelatedToType {
  LEAD
  OPPORTUNITY
  CONTACT
  ACCOUNT
  TASK
  TICKET
  QUOTE
}

enum Currency {
  USD
  CHF
  EUR
  GBP
}

enum OpportunityLineStatus {
  ACTIVE
  INACTIVE
  PENDING
}

enum TaskStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  CANCELLED
}

enum LeadPriority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

enum QuoteStatus {
  DRAFT
  IN_REVIEW
  SENT
  ACCEPTED
  REJECTED
  EXPIRED
  CONVERTED
  CANCELLED
}

enum TicketType {
  ISSUE
  REQUEST
  BUG
  FEEDBACK
  QUESTION
  COMPLAINT
  FEATURE
  OTHER
}

enum TicketChannel {
  EMAIL
  WHATSAPP
  WEBCHAT
  PHONE
  SMS
  TWITTER
  FACEBOOK
  INSTAGRAM
  API
  MANUAL
  OTHER
}

enum TicketCreationSource {
  CONVERSATION
  MANUAL
  API
  IMPORTED
  SYSTEM
}

enum TicketResolutionType {
  AI
  AGENT
  MIXED
  MISSED
  ABANDONED
  REOPENED
  ESCALATED
}

enum ActionType {
  CREATE
  UPDATE
  VIEW
  DELETE
}

enum ActivityVisibility {
  PRIVATE
  INTERNAL
  TEAM
}

enum ApprovalStatus {
  PENDING
  APPROVED
  REJECTED
  CANCELLED
}

enum RoadmapStatus {
  PLANNED
  IN_PROGRESS
  COMPLETED
  ON_HOLD
  CANCELLED
}

enum RoadmapPriority {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}
