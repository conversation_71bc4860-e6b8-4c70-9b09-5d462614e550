"""
Notes Enrich API Router

This module provides AI-powered note enrichment functionality that analyzes
note content to generate enhanced summaries and extract actionable items.
"""

import logging
from typing import Optional, List
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

from core.prompt_loader import prompt_loader
from ai_models.azure_openai import get_azure_openai_model
from langchain.schema import HumanMessage, SystemMessage

logger = logging.getLogger(__name__)

# Create router
router = APIRouter()

# Request/Response models
class EnrichRequest(BaseModel):
    """Request model for note enrichment."""
    noteContent: str
    leadId: Optional[str] = None

class ActionItem(BaseModel):
    """Model for individual action items generated by analysis."""
    type: str  # email, task, event
    title: str

class EnrichResponse(BaseModel):
    """Response model for note enrichment."""
    success: bool
    enhancedSummary: Optional[str] = None
    actions: Optional[List[ActionItem]] = None
    totalActions: Optional[int] = None
    originalLength: Optional[int] = None
    enhancedLength: Optional[int] = None
    error: Optional[str] = None

async def _generate_enhanced_summary(note_content: str) -> str:
    """
    Generate enhanced summary using AI.

    Args:
        note_content: Original note content

    Returns:
        str: Enhanced summary
    """
    try:
        # Get AI model for summarization
        llm = get_azure_openai_model(temperature=0.3, max_tokens=2000)

        # Get the unified prompt for enhancement
        prompt = prompt_loader.get_notes_execute_prompt(note_content)

        # Create messages
        system_message = SystemMessage(content="You are an AI assistant specialized in enhancing CRM notes for sales professionals.")
        human_message = HumanMessage(content=prompt)

        # Get AI response
        response = await llm.create_chat_completion_async([system_message, human_message])
        ai_response = response.content

        # Extract enhanced summary from the response
        if "ENHANCED_SUMMARY:" in ai_response:
            summary_part = ai_response.split("ENHANCED_SUMMARY:")[1]
            if "NEXT_ACTIONS:" in summary_part:
                summary_part = summary_part.split("NEXT_ACTIONS:")[0]
            return summary_part.strip()

        # Fallback to original content if parsing fails
        return note_content

    except Exception as e:
        logger.error(f"Error generating enhanced summary: {e}")
        return note_content


async def _generate_action_items(note_content: str, lead_context: str = "") -> List[ActionItem]:
    """
    Generate action items using direct AI analysis.

    Args:
        note_content: Original note content
        lead_context: Context about the lead

    Returns:
        List[ActionItem]: Generated action items
    """
    try:
        # Get AI model for action generation
        llm = get_azure_openai_model(temperature=0.3, max_tokens=1500)

        # Create a simple prompt for action generation
        context_info = f"\nLead Context: {lead_context}" if lead_context else ""
        action_prompt = f"""
Analyze the following note content and identify actionable items. Generate specific actions that need to be taken based on the content.

Note Content:
{note_content}{context_info}

Please identify actions in these categories:
1. EMAIL actions - emails that need to be sent
2. TASK actions - tasks that need to be completed
3. EVENT actions - meetings/calls that need to be scheduled

For each action, provide:
- type: "email", "task", or "event"
- title: brief description of the action

Respond with a simple list format:
EMAIL: [action title]
TASK: [action title]
EVENT: [action title]

Only include actions that are clearly needed based on the content. If no actions are needed, respond with "No actions required."
"""

        # Create messages
        system_message = SystemMessage(content="You are an AI assistant that identifies actionable items from business notes.")
        human_message = HumanMessage(content=action_prompt)

        # Get AI response
        response = await llm.create_chat_completion_async([system_message, human_message])
        ai_response = response.content.strip()

        logger.info(f"AI action response: {ai_response[:200]}...")

        # Parse the response to extract actions
        action_items = []

        if "No actions required" in ai_response:
            return action_items

        lines = ai_response.split('\n')
        for line in lines:
            line = line.strip()
            if line.startswith('EMAIL:'):
                title = line.replace('EMAIL:', '').strip()
                if title:
                    action_items.append(ActionItem(type="email", title=title))
            elif line.startswith('TASK:'):
                title = line.replace('TASK:', '').strip()
                if title:
                    action_items.append(ActionItem(type="task", title=title))
            elif line.startswith('EVENT:'):
                title = line.replace('EVENT:', '').strip()
                if title:
                    action_items.append(ActionItem(type="event", title=title))

        logger.info(f"Generated {len(action_items)} action items")
        return action_items

    except Exception as e:
        logger.error(f"Error generating action items: {e}")
        return []


@router.post("/enrich", response_model=EnrichResponse, summary="Enrich Notes with AI Analysis")
async def enrich_notes_with_ai(request: EnrichRequest):
    """
    Enrich notes with enhanced summaries and optional action items.

    Args:
        request: EnrichRequest containing noteContent and optional leadId

    Returns:
        EnrichResponse with enhanced summary and action items
    """
    try:
        # Validate required fields
        if not request.noteContent or not request.noteContent.strip():
            raise HTTPException(status_code=400, detail="Note content is required")

        original_content = request.noteContent.strip()

        logger.info(f"Starting note enrichment for content length: {len(original_content)}")

        # Generate enhanced summary using AI
        enhanced_summary = await _generate_enhanced_summary(original_content)

        # Generate action items using CrewAI agents
        lead_context = f"Lead ID: {request.leadId}" if request.leadId else ""
        action_items = await _generate_action_items(original_content, lead_context)

        response_data = {
            "success": True,
            "enhancedSummary": enhanced_summary,
            "actions": action_items,
            "totalActions": len(action_items),
            "originalLength": len(original_content),
            "enhancedLength": len(enhanced_summary)
        }

        logger.info(f"Note enrichment completed: {len(action_items)} actions generated")
        return EnrichResponse(**response_data)

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error in note enrichment: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"An unexpected error occurred: {type(e).__name__}: {str(e)}"
        )
