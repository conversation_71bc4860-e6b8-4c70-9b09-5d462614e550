"""
Lead Action Agent API Router

This module provides endpoints for executing AI agent actions for leads.
"""

import logging
from typing import Optional, Dict, Any
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from datetime import datetime
from agents.lead_agent.database import db_manager

logger = logging.getLogger(__name__)

router = APIRouter()


class ActionAgentRequest(BaseModel):
    """Request model for executing lead action agent."""
    type: str  # Action type (task, email, event)
    title: str  # Action title
    noteContent: str  # Note content for context
    leadId: str  # Lead ID to fetch tenant and owner details


class ActionAgentResponse(BaseModel):
    """Response model for action agent execution."""
    success: bool
    actionId: Optional[str] = None
    status: str
    message: str
    error: Optional[str] = None


@router.post("/action-agent", response_model=ActionAgentResponse, summary="Execute Lead Action Agent")
async def execute_action_agent(request: ActionAgentRequest):
    """
    Execute a lead action agent by creating appropriate records in the database.

    This endpoint takes the action data generated by the AI agent and creates
    the corresponding record (task, email, event) that can be tracked and managed.

    Args:
        request: ActionAgentRequest containing action type, title, note content, and lead ID

    Returns:
        ActionAgentResponse with execution status and action ID
    """
    try:
        logger.info(f"Executing action agent: {request.type}")

        # Validate required fields
        if not request.type:
            raise HTTPException(status_code=400, detail="Action type is required")

        if not request.title:
            raise HTTPException(status_code=400, detail="Action title is required")

        if not request.leadId:
            raise HTTPException(status_code=400, detail="Lead ID is required")

        # For now, only handle task type (can be extended for email/event later)
        if request.type != "task":
            raise HTTPException(status_code=400, detail="Only 'task' type is currently supported")

        # Get lead details if leadId is provided
        lead_data = None
        tenant_id = None
        assignee_id = None

        try:
            # Fetch lead details from database
            lead_data = await db_manager.get_lead_by_id(request.leadId, "")
            if lead_data:
                tenant_id = lead_data.get("tenantId")
                assignee_id = lead_data.get("userId")  # Lead owner
                logger.info(f"Retrieved lead data for ID: {request.leadId}")
            else:
                logger.warning(f"Lead not found for ID: {request.leadId}")
                # Use default values if lead not found
                tenant_id = "default_tenant"
                assignee_id = "default_user"
        except Exception as e:
            logger.error(f"Error fetching lead data: {e}")
            # Use default values if fetch fails
            tenant_id = "default_tenant"
            assignee_id = "default_user"

        # Create task data from the action agent request
        task_data = {
            "title": request.title,
            "description": f"Task generated from note: {request.noteContent[:100]}..." if len(request.noteContent) > 100 else f"Task generated from note: {request.noteContent}",
            "priority": "medium",
            "status": "pending",
            "dueDate": (datetime.now().isoformat() + "Z"),  # Default to current time
            "tenantId": tenant_id,
            "assigneeId": assignee_id,
            "leadId": request.leadId,
            "reference_modal": "Lead",
            "reference_modalId": request.leadId,
        }

        # Create actual task in database using Prisma
        try:
            # Prepare task data for database insertion
            task_create_data = {
                "title": task_data["title"],
                "description": task_data.get("description"),
                "priority": task_data.get("priority", "medium"),
                "status": task_data.get("status", "pending"),
                "reference_modal": task_data["reference_modal"],
                "reference_modalId": task_data["reference_modalId"],
                "deleted": False
            }

            # Add optional fields if present
            if task_data.get("dueDate"):
                try:
                    # Parse ISO date string to datetime
                    due_date = datetime.fromisoformat(task_data["dueDate"].replace('Z', '+00:00'))
                    task_create_data["dueDate"] = due_date
                except (ValueError, AttributeError):
                    logger.warning(f"Invalid due date format: {task_data.get('dueDate')}")

            # Add tenant and assignee if available
            if tenant_id:
                task_create_data["tenantId"] = tenant_id
            if assignee_id:
                task_create_data["assigneeId"] = assignee_id

            # Create task in database - try different model names with detailed logging
            created_task = None
            task_model_names = ['taskitem', 'task', 'TaskItem', 'Task', 'taskItem']

            logger.info(f"Attempting to create task with data: {task_create_data}")
            logger.info(f"Available client attributes: {[attr for attr in dir(db_manager._client) if not attr.startswith('_')]}")

            for model_name in task_model_names:
                try:
                    logger.info(f"Trying model name: {model_name}")

                    if hasattr(db_manager._client, model_name):
                        logger.info(f"✅ Found model: {model_name}")
                        model = getattr(db_manager._client, model_name)
                        logger.info(f"Model type: {type(model)}")

                        if hasattr(model, 'create'):
                            logger.info(f"✅ Model {model_name} has create method")
                            created_task = await model.create(data=task_create_data)
                            logger.info(f"✅ Successfully created task using model: {model_name}")
                            break
                        else:
                            logger.warning(f"❌ Model {model_name} does not have create method")
                    else:
                        logger.warning(f"❌ Model {model_name} not found in client")

                except Exception as model_error:
                    logger.error(f"❌ Failed to create task with model {model_name}: {model_error}")
                    logger.error(f"Error type: {type(model_error).__name__}")
                    continue

            if not created_task:
                # List all available models for debugging
                available_models = [attr for attr in dir(db_manager._client) if not attr.startswith('_') and hasattr(getattr(db_manager._client, attr, None), 'create')]
                logger.error(f"Available models with create method: {available_models}")
                raise Exception(f"Could not create task with any available model. Tried: {task_model_names}. Available models: {available_models}")

            task_id = created_task.id
            logger.info(f"Successfully created task in database with ID: {task_id} for tenant: {tenant_id}, assignee: {assignee_id}")

        except Exception as db_error:
            logger.error(f"Database error creating task: {db_error}")
            # Don't create fake IDs - return error instead
            raise HTTPException(
                status_code=500,
                detail=f"Failed to create task in database: {str(db_error)}"
            )

        return ActionAgentResponse(
            success=True,
            actionId=task_id,
            status="pending",
            message="Task created successfully"
        )

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error executing action agent: {e}")
        return ActionAgentResponse(
            success=False,
            status="failed",
            message="Failed to create task",
            error=str(e)
        )



