"""
Prompt Loader Utility

This module provides utilities for loading and formatting AI prompts from YAML files.
"""

import os
import yaml


class PromptLoader:
    """Utility class for loading and formatting prompts from YAML files."""
    
    def __init__(self):
        """Initialize the prompt loader."""
        # Get the prompts directory path relative to this file
        current_dir = os.path.dirname(os.path.dirname(__file__))  # Go up to backend root
        self.prompts_dir = os.path.join(current_dir, 'prompts')
    
    def load_template(self, template_name: str) -> str:
        """
        Load a prompt template from YAML file.
        
        Args:
            template_name: Name of the template file (without .yml extension)
            
        Returns:
            str: The prompt template content
            
        Raises:
            FileNotFoundError: If the template file doesn't exist
            ValueError: If there's an error parsing the YAML file
        """
        template_path = os.path.join(self.prompts_dir, f"{template_name}.yml")
        
        try:
            with open(template_path, 'r', encoding='utf-8') as file:
                data = yaml.safe_load(file)
                return data.get('template', '')
        except FileNotFoundError:
            raise FileNotFoundError(f"Prompt template file not found: {template_path}")
        except yaml.YAMLError as e:
            raise ValueError(f"Error parsing YAML file {template_path}: {e}")
    
    def get_notes_summarization_prompt(self, note_content: str) -> str:
        """
        Generate the AI prompt for note content summarization.

        Args:
            note_content: The original note content to summarize

        Returns:
            str: The formatted prompt for AI summarization
        """
        template = self.load_template('notes_summarization')

        return template.format(note_content=note_content)

    def get_notes_next_actions_prompt(self, note_content: str) -> str:
        """
        Generate the AI prompt for extracting next actions from notes.

        Args:
            note_content: The original note content to analyze

        Returns:
            str: The formatted prompt for AI next actions analysis
        """
        template = self.load_template('notes_next_actions')

        return template.format(note_content=note_content)

    def get_notes_execute_prompt(self, note_content: str) -> str:
        """
        Generate the AI prompt for unified note enhancement (summary + next actions).

        Args:
            note_content: The original note content to enhance

        Returns:
            str: The formatted prompt for AI unified note enhancement
        """
        template = self.load_template('notes_execute')

        return template.format(note_content=note_content)

    def get_crew_email_agent_prompt(self, note_content: str, lead_context: str = "") -> str:
        """
        Generate the AI prompt for the email draft agent.

        Args:
            note_content: The original note content to analyze
            lead_context: Context about the lead/contact

        Returns:
            str: The formatted prompt for the email draft agent
        """
        template = self.load_template('crew_email_agent')
        return template.format(note_content=note_content, lead_context=lead_context)

    def get_crew_task_agent_prompt(self, note_content: str, lead_context: str = "") -> str:
        """
        Generate the AI prompt for the task creation agent.

        Args:
            note_content: The original note content to analyze
            lead_context: Context about the lead/contact

        Returns:
            str: The formatted prompt for the task creation agent
        """
        template = self.load_template('crew_task_agent')
        return template.format(note_content=note_content, lead_context=lead_context)

    def get_crew_event_agent_prompt(self, note_content: str, lead_context: str = "") -> str:
        """
        Generate the AI prompt for the event creation agent.

        Args:
            note_content: The original note content to analyze
            lead_context: Context about the lead/contact

        Returns:
            str: The formatted prompt for the event creation agent
        """
        template = self.load_template('crew_event_agent')
        return template.format(note_content=note_content, lead_context=lead_context)


# Create a global instance for easy importing
prompt_loader = PromptLoader()
