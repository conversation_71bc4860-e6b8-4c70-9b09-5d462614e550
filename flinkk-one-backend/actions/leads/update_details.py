"""
Unified Update Details Action for Lead Management

This module provides a single CopilotKit action for updating any lead field
including basic info, business info, and custom fields in one unified interface.
"""

from typing import Dict, Any
from actions.base import BaseAction, ActionResult, create_success_response, create_error_response
from core.database import db_manager
from core.validation import validator


class UpdateDetailsAction(BaseAction):
    """Unified action for updating any lead details."""
    
    def __init__(self):
        super().__init__(
            name="updateDetails",
            description="Update any lead details including basic information (name, email, phone, company), business information (title, industry, source, website), and custom fields.",
            parameters=[
                {
                    "name": "leadId",
                    "type": "string",
                    "description": "The unique identifier of the lead to update",
                    "required": True
                },
                # Basic Information Fields
                {
                    "name": "firstName",
                    "type": "string",
                    "description": "First name of the lead",
                    "required": False
                },
                {
                    "name": "lastName",
                    "type": "string",
                    "description": "Last name of the lead",
                    "required": False
                },
                {
                    "name": "email",
                    "type": "string",
                    "description": "Email address of the lead",
                    "required": False
                },
                {
                    "name": "phone",
                    "type": "string",
                    "description": "Phone number of the lead",
                    "required": False
                },
                {
                    "name": "company",
                    "type": "string",
                    "description": "Company name of the lead",
                    "required": False
                },
                # Business Information Fields
                {
                    "name": "title",
                    "type": "string",
                    "description": "Job title of the lead",
                    "required": False
                },
                {
                    "name": "industry",
                    "type": "string",
                    "description": "Industry of the lead's company",
                    "required": False
                },
                {
                    "name": "source",
                    "type": "string",
                    "description": "Source where the lead came from",
                    "required": False
                },
                {
                    "name": "website",
                    "type": "string",
                    "description": "Website URL of the lead's company",
                    "required": False
                },
                # Custom Fields
                {
                    "name": "customFields",
                    "type": "object",
                    "description": "Object containing custom field values",
                    "required": False
                }
            ],
            require_auth=False,
            validate_tenant=False
        )
    
    async def _custom_validation(self, args: Dict[str, Any]) -> ActionResult:
        """Custom validation for all lead fields."""
        try:
            print(f"🔍 [UPDATE-DETAILS-VALIDATION] Starting unified validation")
            print(f"📋 [UPDATE-DETAILS-VALIDATION] Input args: {args}")
            
            # Separate fields by category
            basic_info_fields = {}
            business_info_fields = {}
            custom_fields = args.get("customFields", {})
            
            # Extract basic info fields
            for field in ["firstName", "lastName", "email", "phone", "company"]:
                if field in args and args[field] is not None:
                    print(f"📝 [UPDATE-DETAILS-VALIDATION] Processing basic field '{field}': {args[field]}")
                    if field == "phone":
                        cleaned_phone = validator.clean_phone_number(args[field])
                        basic_info_fields[field] = cleaned_phone
                        print(f"📞 [UPDATE-DETAILS-VALIDATION] Cleaned phone: {cleaned_phone}")
                    else:
                        basic_info_fields[field] = args[field]
            
            # Extract business info fields
            for field in ["title", "industry", "source", "website"]:
                if field in args and args[field] is not None:
                    print(f"📝 [UPDATE-DETAILS-VALIDATION] Processing business field '{field}': {args[field]}")
                    business_info_fields[field] = args[field]
            
            print(f"📋 [UPDATE-DETAILS-VALIDATION] Basic info fields: {basic_info_fields}")
            print(f"📋 [UPDATE-DETAILS-VALIDATION] Business info fields: {business_info_fields}")
            print(f"📋 [UPDATE-DETAILS-VALIDATION] Custom fields: {custom_fields}")
            
            # Validate basic info if any fields provided
            if basic_info_fields:
                print(f"🔍 [UPDATE-DETAILS-VALIDATION] Validating basic info...")
                is_valid, validation_error = validator.validate_basic_info(basic_info_fields)
                if not is_valid:
                    print(f"❌ [UPDATE-DETAILS-VALIDATION] Basic info validation failed: {validation_error}")
                    return ActionResult(
                        success=False,
                        response=create_error_response(
                            message=validation_error,
                            error_code="BASIC_INFO_VALIDATION_FAILED"
                        )
                    )
                print(f"✅ [UPDATE-DETAILS-VALIDATION] Basic info validation passed")
            
            # Validate business info if any fields provided
            if business_info_fields:
                print(f"🔍 [UPDATE-DETAILS-VALIDATION] Validating business info...")
                is_valid, validation_error = validator.validate_business_info(business_info_fields)
                if not is_valid:
                    print(f"❌ [UPDATE-DETAILS-VALIDATION] Business info validation failed: {validation_error}")
                    return ActionResult(
                        success=False,
                        response=create_error_response(
                            message=validation_error,
                            error_code="BUSINESS_INFO_VALIDATION_FAILED"
                        )
                    )
                print(f"✅ [UPDATE-DETAILS-VALIDATION] Business info validation passed")
            
            # Store cleaned data for use in execution
            cleaned_data = {
                "basic_info": basic_info_fields,
                "business_info": business_info_fields,
                "custom_fields": custom_fields
            }
            args["_cleaned_data"] = cleaned_data
            print(f"💾 [UPDATE-DETAILS-VALIDATION] Stored cleaned data in args")
            
            return ActionResult(
                success=True,
                response=create_success_response("Unified validation passed")
            )
            
        except Exception as e:
            print(f"❌ [UPDATE-DETAILS-VALIDATION] Validation failed with exception: {str(e)}")
            return ActionResult(
                success=False,
                response=create_error_response(
                    message=f"Validation error: {str(e)}",
                    error_code="VALIDATION_ERROR"
                )
            )
    
    async def _execute_action(self, args: Dict[str, Any]) -> ActionResult:
        """Execute the unified update details action."""
        try:
            print(f"🔧 [UPDATE-DETAILS] Starting unified update details action")
            print(f"📋 [UPDATE-DETAILS] Received args: {args}")
            
            # Use default values for tenant and user (no authentication required)
            tenant_id = "default_tenant"
            user_id = "default_user"
            print(f"🔐 [UPDATE-DETAILS] Using default context - tenant_id: {tenant_id}, user_id: {user_id}")
            
            # Get lead ID
            lead_id = args.get("leadId")
            if not lead_id:
                print(f"❌ [UPDATE-DETAILS] No leadId found in args")
                return ActionResult(
                    success=False,
                    response=create_error_response(
                        message="Lead ID is required",
                        error="leadId parameter missing",
                        error_code="LEAD_ID_MISSING"
                    )
                )
            
            print(f"📝 [UPDATE-DETAILS] Lead ID: {lead_id}")
            
            # Get cleaned data from validation
            cleaned_data = args.get("_cleaned_data", {})
            basic_info = cleaned_data.get("basic_info", {})
            business_info = cleaned_data.get("business_info", {})
            custom_fields = cleaned_data.get("custom_fields", {})
            
            print(f"📝 [UPDATE-DETAILS] Basic info updates: {basic_info}")
            print(f"📝 [UPDATE-DETAILS] Business info updates: {business_info}")
            print(f"📝 [UPDATE-DETAILS] Custom fields updates: {custom_fields}")
            
            # Perform bulk update using existing database manager
            print(f"💾 [UPDATE-DETAILS] Calling database manager for bulk update...")
            result = await db_manager.bulk_update_lead_fields(
                lead_id=lead_id,
                tenant_id=tenant_id,
                user_id=user_id,
                basic_info=basic_info if basic_info else None,
                business_info=business_info if business_info else None,
                custom_fields=custom_fields if custom_fields else None
            )
            print(f"✅ [UPDATE-DETAILS] Database update completed: {result}")
            
            # Prepare response data
            all_updated_fields = []
            if basic_info:
                all_updated_fields.extend(basic_info.keys())
            if business_info:
                all_updated_fields.extend(business_info.keys())
            if custom_fields:
                all_updated_fields.extend(custom_fields.keys())
            
            action_result = ActionResult(
                success=True,
                response=create_success_response(
                    message=result["message"],
                    data={
                        "leadId": lead_id,
                        "updatedFields": all_updated_fields,
                        "updatedCategories": result["updated_categories"],
                        "totalChanges": result["total_changes"],
                        "changes": result["changes"]
                    },
                    metadata={
                        "action": "updateDetails",
                        "tenant_id": tenant_id,
                        "user_id": user_id
                    }
                )
            )
            print(f"✅ [UPDATE-DETAILS] Action completed successfully")
            return action_result
            
        except ValueError as e:
            print(f"❌ [UPDATE-DETAILS] ValueError: {str(e)}")
            return ActionResult(
                success=False,
                response=create_error_response(
                    message=str(e),
                    error="Lead not found or access denied",
                    error_code="LEAD_NOT_FOUND"
                )
            )
        except Exception as e:
            print(f"❌ [UPDATE-DETAILS] Unexpected error: {str(e)}")
            return ActionResult(
                success=False,
                response=create_error_response(
                    message="Failed to update lead details. Please try again.",
                    error=str(e),
                    error_code="UPDATE_FAILED"
                )
            )
