"""
Lead Management Actions

This module contains all CopilotKit actions related to lead management operations.
Now simplified to use a single unified UpdateDetails action.
"""

from typing import List
from copilotkit import Action

from .update_details import UpdateDetailsAction

# Create action instance
update_details_action_instance = UpdateDetailsAction()

# Export CopilotKit Action objects
lead_actions: List[Action] = [
    update_details_action_instance.create_copilotkit_action(),
]

# Export action instances for direct use
__all__ = [
    "lead_actions",
    "update_details_action_instance"
]
