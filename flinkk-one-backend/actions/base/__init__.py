"""
Base Action Classes and Utilities

This module provides base classes and utilities for creating CopilotKit actions
with consistent error handling, validation, and response formatting.
"""

from .action_base import BaseAction, ActionResult
from .response_models import (
    SuccessResponse,
    ErrorResponse,
    ValidationErrorResponse,
    AuthErrorResponse,
    create_success_response,
    create_error_response,
    create_validation_error_response,
    create_auth_error_response
)

__all__ = [
    "BaseAction",
    "ActionResult",
    "SuccessResponse",
    "ErrorResponse",
    "ValidationErrorResponse",
    "AuthErrorResponse",
    "create_success_response",
    "create_error_response",
    "create_validation_error_response",
    "create_auth_error_response"
]
