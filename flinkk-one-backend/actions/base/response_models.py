"""
Standard Response Models for CopilotKit Actions

Provides consistent response formats across all actions for better error handling
and client-side processing.
"""

from typing import Any, Dict, List, Optional, Union
from pydantic import BaseModel, Field
from enum import Enum


class ResponseStatus(str, Enum):
    """Standard response status values."""
    SUCCESS = "success"
    ERROR = "error"
    VALIDATION_ERROR = "validation_error"
    AUTH_ERROR = "auth_error"
    NOT_FOUND = "not_found"
    PERMISSION_DENIED = "permission_denied"


class BaseResponse(BaseModel):
    """Base response model for all CopilotKit actions."""
    success: bool = Field(..., description="Whether the action was successful")
    status: ResponseStatus = Field(..., description="Response status code")
    message: str = Field(..., description="Human-readable message")
    timestamp: Optional[str] = Field(None, description="Response timestamp")
    request_id: Optional[str] = Field(None, description="Unique request identifier")


class SuccessResponse(BaseResponse):
    """Success response model."""
    success: bool = Field(default=True)
    status: ResponseStatus = Field(default=ResponseStatus.SUCCESS)
    data: Optional[Dict[str, Any]] = Field(None, description="Response data")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")


class ErrorResponse(BaseResponse):
    """Error response model."""
    success: bool = Field(default=False)
    status: ResponseStatus = Field(default=ResponseStatus.ERROR)
    error: Optional[str] = Field(None, description="Technical error details")
    error_code: Optional[str] = Field(None, description="Error code for client handling")


class ValidationErrorResponse(ErrorResponse):
    """Validation error response model."""
    status: ResponseStatus = Field(default=ResponseStatus.VALIDATION_ERROR)
    validation_errors: Optional[List[Dict[str, str]]] = Field(
        None, 
        description="List of validation errors"
    )


class AuthErrorResponse(ErrorResponse):
    """Authentication error response model."""
    status: ResponseStatus = Field(default=ResponseStatus.AUTH_ERROR)
    auth_required: bool = Field(default=True, description="Whether authentication is required")


class NotFoundResponse(ErrorResponse):
    """Not found error response model."""
    status: ResponseStatus = Field(default=ResponseStatus.NOT_FOUND)
    resource_type: Optional[str] = Field(None, description="Type of resource not found")
    resource_id: Optional[str] = Field(None, description="ID of resource not found")


class PermissionDeniedResponse(ErrorResponse):
    """Permission denied error response model."""
    status: ResponseStatus = Field(default=ResponseStatus.PERMISSION_DENIED)
    required_permissions: Optional[List[str]] = Field(
        None, 
        description="List of required permissions"
    )


# Union type for all possible responses
ActionResponse = Union[
    SuccessResponse,
    ErrorResponse,
    ValidationErrorResponse,
    AuthErrorResponse,
    NotFoundResponse,
    PermissionDeniedResponse
]


def create_success_response(
    message: str,
    data: Optional[Dict[str, Any]] = None,
    metadata: Optional[Dict[str, Any]] = None
) -> SuccessResponse:
    """Create a standardized success response."""
    return SuccessResponse(
        message=message,
        data=data,
        metadata=metadata
    )


def create_error_response(
    message: str,
    error: Optional[str] = None,
    error_code: Optional[str] = None
) -> ErrorResponse:
    """Create a standardized error response."""
    return ErrorResponse(
        message=message,
        error=error,
        error_code=error_code
    )


def create_validation_error_response(
    message: str,
    validation_errors: Optional[List[Dict[str, str]]] = None
) -> ValidationErrorResponse:
    """Create a standardized validation error response."""
    return ValidationErrorResponse(
        message=message,
        validation_errors=validation_errors
    )


def create_auth_error_response(
    message: str = "Authentication required"
) -> AuthErrorResponse:
    """Create a standardized authentication error response."""
    return AuthErrorResponse(message=message)
