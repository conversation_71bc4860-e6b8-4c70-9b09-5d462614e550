"""
Base Action Class for CopilotKit Actions

Provides a standardized base class for creating CopilotKit actions with consistent
error handling, validation, authentication, and response formatting.
"""

import logging
import uuid
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Any, Dict, List, Optional, Type
from pydantic import BaseModel, ValidationError

from copilotkit import Action
from core.auth.context import get_auth_context
from core.validation import validator
from .response_models import (
    ActionResponse,
    create_success_response,
    create_error_response,
    create_validation_error_response,
    create_auth_error_response
)


logger = logging.getLogger(__name__)


class ActionResult:
    """Container for action execution results."""
    
    def __init__(
        self,
        success: bool,
        response: ActionResponse,
        execution_time: Optional[float] = None,
        request_id: Optional[str] = None
    ):
        self.success = success
        self.response = response
        self.execution_time = execution_time
        self.request_id = request_id or str(uuid.uuid4())
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert result to dictionary format expected by CopilotKit."""
        result = self.response.dict()
        result["request_id"] = self.request_id
        result["timestamp"] = datetime.utcnow().isoformat()
        if self.execution_time:
            result["execution_time"] = self.execution_time
        return result


class BaseAction(ABC):
    """
    Base class for all CopilotKit actions.
    
    Provides standardized:
    - Error handling and logging
    - Authentication and authorization
    - Input validation
    - Response formatting
    - Execution tracking
    """
    
    def __init__(
        self,
        name: str,
        description: str,
        parameters: List[Dict[str, Any]],
        require_auth: bool = False,
        validate_tenant: bool = False
    ):
        self.name = name
        self.description = description
        self.parameters = parameters
        self.require_auth = require_auth
        self.validate_tenant = validate_tenant
        self.logger = logging.getLogger(f"action.{name}")
    
    async def execute(self, **kwargs) -> Dict[str, Any]:
        """
        Main execution method called by CopilotKit.

        Handles authentication, validation, execution, and response formatting.
        CopilotKit passes parameters as keyword arguments, so we accept **kwargs
        and convert them to a dictionary for internal processing.
        """
        # Convert kwargs to args dictionary for internal processing
        args = dict(kwargs)

        request_id = str(uuid.uuid4())
        start_time = datetime.utcnow()

        # Console logging for debugging
        print(f"🚀 [ACTION-BASE] Starting action execution: {self.name}")
        print(f"📝 [ACTION-BASE] Request ID: {request_id}")
        print(f"📋 [ACTION-BASE] Received kwargs: {kwargs}")
        print(f"📋 [ACTION-BASE] Converted args: {args}")

        try:
            self.logger.info(f"Executing action {self.name} [request_id: {request_id}]")

            # Skip authentication entirely
            print(f"🔓 [ACTION-BASE] Authentication disabled - proceeding without auth checks")

            # Input validation
            print(f"🔍 [ACTION-BASE] Starting input validation...")
            validation_result = await self._validate_input(args)
            if not validation_result.success:
                print(f"❌ [ACTION-BASE] Input validation failed: {validation_result.response}")
                return validation_result.to_dict()
            print(f"✅ [ACTION-BASE] Input validation successful")

            # Execute the action
            print(f"⚡ [ACTION-BASE] Executing action logic...")
            result = await self._execute_action(args)
            print(f"📊 [ACTION-BASE] Action execution result: {result.success}")

            # Calculate execution time
            execution_time = (datetime.utcnow() - start_time).total_seconds()
            result.execution_time = execution_time
            result.request_id = request_id

            print(f"✅ [ACTION-BASE] Action {self.name} completed successfully in {execution_time:.3f}s")
            self.logger.info(
                f"Action {self.name} completed successfully in {execution_time:.3f}s "
                f"[request_id: {request_id}]"
            )

            final_result = result.to_dict()
            print(f"📤 [ACTION-BASE] Final response: {final_result}")
            return final_result

        except Exception as e:
            execution_time = (datetime.utcnow() - start_time).total_seconds()
            print(f"❌ [ACTION-BASE] Action {self.name} failed after {execution_time:.3f}s: {str(e)}")
            self.logger.error(
                f"Action {self.name} failed after {execution_time:.3f}s: {str(e)} "
                f"[request_id: {request_id}]"
            )

            error_result = ActionResult(
                success=False,
                response=create_error_response(
                    message=f"Action {self.name} failed",
                    error=str(e),
                    error_code="EXECUTION_ERROR"
                ),
                execution_time=execution_time,
                request_id=request_id
            )

            final_error_result = error_result.to_dict()
            print(f"📤 [ACTION-BASE] Error response: {final_error_result}")
            return final_error_result
    
    async def _authenticate(self, args: Dict[str, Any]) -> ActionResult:
        """Handle authentication and authorization."""
        try:
            print(f"🔐 [AUTH] Getting authentication context...")
            tenant_id, user_id = await get_auth_context()
            print(f"🔐 [AUTH] Retrieved context - tenant_id: {tenant_id}, user_id: {user_id}")

            if self.validate_tenant:
                print(f"🔐 [AUTH] Validating tenant context...")
                is_valid, error_msg = validator.validate_authentication_context(tenant_id, user_id)
                if not is_valid:
                    print(f"❌ [AUTH] Tenant validation failed: {error_msg}")
                    return ActionResult(
                        success=False,
                        response=create_auth_error_response(error_msg)
                    )
                print(f"✅ [AUTH] Tenant validation successful")
            else:
                print(f"🔐 [AUTH] Tenant validation skipped")

            # Store auth context in args for use in action
            args["_auth_context"] = {"tenant_id": tenant_id, "user_id": user_id}
            print(f"✅ [AUTH] Authentication context stored in args")

            return ActionResult(
                success=True,
                response=create_success_response("Authentication successful")
            )

        except Exception as e:
            print(f"❌ [AUTH] Authentication failed with exception: {str(e)}")
            return ActionResult(
                success=False,
                response=create_auth_error_response(f"Authentication failed: {str(e)}")
            )
    
    async def _validate_input(self, args: Dict[str, Any]) -> ActionResult:
        """Validate input parameters."""
        try:
            print(f"🔍 [VALIDATION] Starting parameter validation for {len(self.parameters)} parameters")
            # Basic parameter validation
            validation_errors = []

            for param in self.parameters:
                param_name = param["name"]
                is_required = param.get("required", False)
                param_type = param.get("type", "string")

                print(f"🔍 [VALIDATION] Checking parameter '{param_name}' (required: {is_required}, type: {param_type})")

                if is_required and param_name not in args:
                    error_msg = f"Required parameter '{param_name}' is missing"
                    print(f"❌ [VALIDATION] {error_msg}")
                    validation_errors.append({
                        "field": param_name,
                        "error": error_msg
                    })
                    continue

                if param_name in args:
                    # Type validation
                    value = args[param_name]
                    print(f"🔍 [VALIDATION] Parameter '{param_name}' value: {value} (type: {type(value).__name__})")
                    if not self._validate_parameter_type(value, param_type):
                        error_msg = f"Parameter '{param_name}' must be of type {param_type}"
                        print(f"❌ [VALIDATION] {error_msg}")
                        validation_errors.append({
                            "field": param_name,
                            "error": error_msg
                        })
                    else:
                        print(f"✅ [VALIDATION] Parameter '{param_name}' type validation passed")
                else:
                    print(f"ℹ️ [VALIDATION] Optional parameter '{param_name}' not provided")

            if validation_errors:
                print(f"❌ [VALIDATION] Validation failed with {len(validation_errors)} errors: {validation_errors}")
                return ActionResult(
                    success=False,
                    response=create_validation_error_response(
                        message="Input validation failed",
                        validation_errors=validation_errors
                    )
                )

            print(f"✅ [VALIDATION] Basic parameter validation passed")

            # Custom validation
            print(f"🔍 [VALIDATION] Running custom validation...")
            custom_validation = await self._custom_validation(args)
            if not custom_validation.success:
                print(f"❌ [VALIDATION] Custom validation failed")
                return custom_validation

            print(f"✅ [VALIDATION] All validation checks passed")
            return ActionResult(
                success=True,
                response=create_success_response("Input validation successful")
            )

        except Exception as e:
            print(f"❌ [VALIDATION] Validation failed with exception: {str(e)}")
            return ActionResult(
                success=False,
                response=create_validation_error_response(f"Validation error: {str(e)}")
            )
    
    def _validate_parameter_type(self, value: Any, expected_type: str) -> bool:
        """Validate parameter type."""
        type_mapping = {
            "string": str,
            "number": (int, float),
            "boolean": bool,
            "array": list,
            "object": dict
        }
        
        expected_python_type = type_mapping.get(expected_type)
        if expected_python_type is None:
            return True  # Unknown type, skip validation
        
        return isinstance(value, expected_python_type)
    
    async def _custom_validation(self, args: Dict[str, Any]) -> ActionResult:
        """Override this method for custom validation logic."""
        return ActionResult(
            success=True,
            response=create_success_response("Custom validation passed")
        )
    
    @abstractmethod
    async def _execute_action(self, args: Dict[str, Any]) -> ActionResult:
        """
        Execute the actual action logic.
        
        This method must be implemented by subclasses.
        """
        pass
    
    def create_copilotkit_action(self) -> Action:
        """Create the CopilotKit Action object."""
        return Action(
            name=self.name,
            description=self.description,
            parameters=self.parameters,
            handler=self.execute
        )
